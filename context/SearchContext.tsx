"use client";

import { useState, useEffect } from "react";
import { Eye, EyeOff } from "lucide-react"; // Optional: use icons if available
import CustomerEdit from "@/components/customer/customer-edit"; // Import the CustomerEdit component
import { CustomerType, usageData, customers, UsageRecord } from "@/app/data/customers"; // Import customers
import { Button } from "@/components/ui/button";
import RandomLineGraph from "./random-line-graph";
import { ListRestart, UserAdd, UserPen } from "@/components/icons/list";
import { Imprima } from "next/font/google";

interface CustomerViewProps {
  selectedCustomer: any; // Replace 'any' with your actual CustomerType
  // This prop will be a function from the parent to go back to the list
  onBackToList: () => void;
  // This prop will be a function from the parent to trigger the edit mode
  onEditCustomer: (customer: any) => void; // Pass the customer to edit
}

const TABS = [
  "Customer Details",
  "Router Info",
  // "Tickets",
  // "Subscription",
  "Usage",
  "Graph",
] as const;

type Props = {
  selectedCustomer: CustomerType;
  setSelectedCustomer: (c: CustomerType | null) => void;
};

export default function CustomerView({
  selectedCustomer,
  setSelectedCustomer,
}: Props) {
  // --- Calculate Default Dates ---

  const [showPassword, setShowPassword] = useState(false);
  const [isEditing, setIsEditing] = useState(false); // New state to manage edit mode
  const [showForm, setShowForm] = useState(false); // State to control visibility of the CustomerEdit form

  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
  const day = String(today.getDate()).padStart(2, "0");

  const defaultStartDate = `${year}-${month}-01`;
  const defaultEndDate = `${year}-${month}-${day}`;

  const [activeTab, setActiveTab] = useState<(typeof TABS)[number]>(TABS[0]);
  const [startDate, setStartDate] = useState<string>(defaultStartDate);
  const [endDate, setEndDate] = useState<string>(defaultEndDate);
  const [appliedStartDate, setAppliedStartDate] =
    useState<string>(defaultStartDate);
  const [appliedEndDate, setAppliedEndDate] = useState<string>(defaultEndDate);

  const [selectedGraphRange, setSelectedGraphRange] = useState<string>("1day");
  // State for the image popup (modal)
  const [showImagePopup, setShowImagePopup] = useState(false);
  const [currentImageSrc, setCurrentImageSrc] = useState<string | null>(null);

  useEffect(() => {
    setStartDate(defaultStartDate);
    setEndDate(defaultEndDate);
    setAppliedStartDate(defaultStartDate);
    setAppliedEndDate(defaultEndDate);
  }, [selectedCustomer.id, activeTab]);

  // Filter usage data for the selected customer
  const customerUsageData = usageData.filter(
    (record) => record.customerId === selectedCustomer.id
  );

  // Sort and get the last 3 usage records for the "Customer Details" section
  const lastThreeUsageRecords = [...customerUsageData] // Create a shallow copy to avoid mutating original array
    .sort(
      (a, b) =>
        new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    ) // Sort by startTime descending
    .slice(0, 3); // Get the first 3 (which are the latest)

  // Filter the full usage data by date range for the "Usage" tab
  const filteredUsageByDate = customerUsageData.filter((record) => {
    const recordStartTimestamp = new Date(record.startTime).getTime();
    const recordEndTimestamp = new Date(record.endTime).getTime();

    const filterStartTimestamp = appliedStartDate
      ? new Date(appliedStartDate + "T00:00:00").getTime()
      : null;
    const filterEndTimestamp = appliedEndDate
      ? new Date(appliedEndDate + "T23:59:59").getTime() // Use endDate from input for filtering on submit
      : null;

    let passesFilter = true;

    if (
      filterStartTimestamp !== null &&
      recordEndTimestamp < filterStartTimestamp
    ) {
      passesFilter = false;
    }

    if (
      filterEndTimestamp !== null &&
      recordStartTimestamp > filterEndTimestamp
    ) {
      passesFilter = false;
    }

    return passesFilter;
  });

  const handleDateSubmit = () => {
    setAppliedStartDate(startDate);
    setAppliedEndDate(endDate);
    console.log("Applying usage data filter for:", startDate, "to", endDate);
  };

  const handleImageClick = (src: string) => {
    setCurrentImageSrc(src);
    setShowImagePopup(true);
  };

  const closeImagePopup = () => {
    setShowImagePopup(false);
    setCurrentImageSrc(null);
  };

  const handleEditCustomer = (customer: CustomerType) => {
    setSelectedCustomer(customer); // Set the customer to be edited
    setShowForm(true); // Show the CustomerEdit form
    //scrollToTop(); // Assuming you have this function defined elsewhere if needed
  };

  const handleFormClose = () => {
    setShowForm(false); // Hide the form when it's closed
  };

  const graphRanges = [
    { label: "1 Day", value: "1day" },
    { label: "3 Days", value: "3days" },
    { label: "7 Days", value: "7days" },
    { label: "15 Days", value: "15days" },
    { label: "1 Month", value: "1month" },
    { label: "3 Months", value: "3months" },
    { label: "6 Months", value: "6months" },
    { label: "1 Year", value: "1year" },
  ];

  if (showForm) {
    return <CustomerEdit customer={selectedCustomer} onClose={handleFormClose} />;
  }

  return (
    <div className="mb-5 space-y-4">
      {" "}
      {/* OUTERMOST DIV START */}
      {/* --- Header bar with name / status / address --- */}
      <div className="grid grid-cols-1 md:grid-cols-3 items-center p-2 rounded-lg  text-sm gap-2">
        <div className="font-semibold text-center md:text-left">
          {selectedCustomer.name}
        </div>
        <div className="text-center">
          Status:{" "}
          <span
            className={`font-medium ${
              selectedCustomer.status === "Active"
                ? "text-green-600"
                : selectedCustomer.status === "Inactive"
                ? "text-yellow-600"
                : "text-red-600"
            }`}
          >
            {selectedCustomer.status}
          </span>
        </div>
        <div className="text-center md:text-right text-gray-700">
          Address: {selectedCustomer.address}
        </div>
      </div>{" "}
      {/* HEADER BAR DIV END */}
      {/* --- Tabs bar --- */}
      <div className="flex flex-wrap justify-between text-center bg-gray-50 border-b rounded-lg border-gray-300">
        {TABS.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex-1 min-w-[140px] py-2 text-sm ${
              activeTab === tab
                ? "border-b-2 border-blue-600 text-blue-600 font-medium"
                : "text-gray-700 hover:text-gray-900"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>{" "}
      {/* TABS BAR DIV END */}
      {/* --- Content area based on activeTab --- */}
      <div>
        {activeTab === "Customer Details" && (
          <div className="grid grid-cols-1 gap-4">
            {/* This outer grid for the entire tab */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Basic Info */}
              <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
                <h3 className="font-semibold mb-2 bg-[#0e1e2e] p-2 rounded text-white">
                  Basic Info
                </h3>
                {/* Add overflow-x-auto to make the table horizontally scrollable on small screens */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="bg-white divide-y divide-gray-200">
                      {/* Customer Identification */}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Customer Name
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.name || "N/A"}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Username
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.username || "N/A"}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Password
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.password ? (
                            <div className="flex items-center">
                              <span className="mr-2">
                                {showPassword
                                  ? selectedCustomer.password
                                  : "*********"}
                              </span>
                              <button
                                onClick={() => setShowPassword(!showPassword)}
                                className="text-gray-500 hover:text-gray-700 focus:outline-none"
                                aria-label={
                                  showPassword
                                    ? "Hide password"
                                    : "Show password"
                                }
                              >
                                {showPassword ? (
                                  <EyeOff size={18} />
                                ) : (
                                  <Eye size={18} />
                                )}
                              </button>
                            </div>
                          ) : (
                            "N/A"
                          )}
                        </td>
                      </tr>
                      {selectedCustomer.nationalId && ( // Keep optional for sensitive info if not always present
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            National ID
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.nationalId || "N/A"}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.dateOfBirth && ( // Keep optional
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            DOB
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.dateOfBirth || "N/A"}
                          </td>
                        </tr>
                      )}

                      {/* Contact Information */}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Mobile
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.mobile || "N/A"}
                        </td>
                      </tr>
                      {selectedCustomer.altMobile && ( // Keep optional as not everyone has an alt mobile
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Alt. Mobile
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.altMobile || "N/A"}
                          </td>
                        </tr>
                      )}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Email
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.email || "N/A"}
                        </td>
                      </tr>

                      {/* Organizational/Company Details */}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Company
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.company || "N/A"}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Organization
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.organization || "N/A"}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Branch
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.branch || "N/A"}
                        </td>
                      </tr>

                      {/* Location & Network Details */}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Address
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.address || "N/A"}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Geo Location
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.geoLocation
                            ? (() => {
                                const [latitude, longitude] =
                                  selectedCustomer.geoLocation
                                    .split(",")
                                    .map((coord) => coord.trim());
                                return (
                                  <a
                                    href={`https://www.google.com/maps/search/?api=1&query=$${latitude},${longitude}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-blue-600 hover:underline"
                                  >
                                    Location (Click Here)
                                  </a>
                                );
                              })()
                            : "N/A"}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Apply FUP
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.applyFUP === "Yes" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800">
                              Yes
                            </span>
                          ) : selectedCustomer.applyFUP === "No" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                              No
                            </span>
                          ) : (
                            <span>{selectedCustomer.applyFUP || "N/A"}</span>
                          )}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Registered
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.registered || "N/A"}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              {/* Service & Package Info */}
              <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
                <h3 className="font-semibold mb-2 bg-[#0e1e2e] p-2 rounded text-white">
                  Service & Package Info
                </h3>
                {/* Wrap the table in overflow-x-auto for responsiveness */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Package
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.package}
                        </td>
                      </tr>
                      {selectedCustomer.bandwidthDownload && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Download
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.bandwidthDownload}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.bandwidthUpload && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Upload
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.bandwidthUpload}
                          </td>
                        </tr>
                      )}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Expiration
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.expiration}
                        </td>
                      </tr>
                      {selectedCustomer.contractType && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Contract
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.contractType}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.lastPaymentDate && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Last Paid
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.lastPaymentDate}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.nextBillDueDate && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Next Bill
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.nextBillDueDate}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.outstandingBalance && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Outstanding
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.outstandingBalance}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
              {/* Connection & Address Info */}
              <div className="bg-white p-4 rounded-lg border shadow-sm text-xs">
                <h3 className="font-semibold mb-2 bg-[#0e1e2e] p-2 rounded text-white">
                  Connection & Address Info
                </h3>
                {/* Wrap the table in overflow-x-auto for horizontal scrolling on small screens */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Service Address
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.address}
                        </td>
                      </tr>
                      {selectedCustomer.landmark && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Landmark
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.landmark}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.ip && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            Assigned IP
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.ip}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.mac && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            CPE MAC
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.mac}
                          </td>
                        </tr>
                      )}
                      {selectedCustomer.nasIp && (
                        <tr>
                          <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                            NAS IP
                          </td>
                          <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                            {selectedCustomer.nasIp}
                          </td>
                        </tr>
                      )}
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Session Status:
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap">
                          {/* First, check if the customer's overall status is "Expired" */}
                          {selectedCustomer.status === "Expired" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                              Expired
                            </span>
                          ) : // If not "Expired", then proceed with checking the actual sessionStatus
                          selectedCustomer.sessionStatus === "Online" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800">
                              Online
                            </span>
                          ) : selectedCustomer.sessionStatus === "Offline" ? (
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                              Offline
                            </span>
                          ) : (
                            // Fallback for any other sessionStatus value (e.g., null, undefined, or other string)
                            <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-gray-100 text-gray-800">
                              {selectedCustomer.sessionStatus || "-"}
                            </span>
                          )}
                        </td>
                      </tr>
                      <tr>
                        <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                          Online Duration
                        </td>
                        <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                          {selectedCustomer.onlineDuration || "-"}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            {/* --- Last 3 Usage Sessions (Table Format) --- */}
            <div className="bg-white p-4 rounded-lg border  shadow-sm text-xs overflow-x-auto">
              <h3 className="font-semibold mb-3 text-sm">Last 3 Sessions</h3>
              {lastThreeUsageRecords.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-100">
                    <tr>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Start Time
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        End Time
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Session
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Download
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        Upload
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        IP Address
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                      >
                        MAC Address
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {lastThreeUsageRecords.map((record, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.startTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.endTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.sessionTime}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.download}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.upload}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.ipAddress}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                          {record.macAddress}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-gray-500">No recent usage sessions found.</p>
              )}
            </div>
          </div>
        )}
      </div>
      {/* CONTENT AREA DIV END */}
      {/* --- Back & Edit buttons --- */}

      <div className="flex flex-col sm:flex-row gap-2 mt-4">
        <Button size="sm" onClick={() => setSelectedCustomer(null)}>
          <ListRestart /> Back to List{" "}
        </Button>
        <Button size="sm" variant="ghost" onClick={() => handleEditCustomer(selectedCustomer)} >
          <UserPen /> Edit Customer
        </Button>
      </div>
      {/* BUTTONS DIV END */}

    </div> /* OUTERMOST DIV END */
  );
}