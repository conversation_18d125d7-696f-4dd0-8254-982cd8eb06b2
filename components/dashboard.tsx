"use client";

import Link from "next/link";
import { useState, useEffect } from "react"; // Import useEffect
import { useRouter } from "next/navigation"; // Correct import for App Router
// import { customers, CustomerType } from "@/app/data/customers"; // REMOVE THIS LINE
import Cookies from "js-cookie"; // Import Cookies
import apiClient from "@/lib/apiClient"; // Import apiClient

import {
  LayoutGrid,
  Users,
  Ticket,
  BarChart3,
  Bell,
  Activity,
  TrendingUp,
  DollarSign,
  MinusCircle,
  XCircle,
  UsersRound,
} from "lucide-react";

// Define CustomerType interface (you might want to put this in a shared types file)
interface CustomerType {
  id: number;
  first_name: string;
  last_name: string;
  username: string;
  password: string;
  contact: string[];
  email: string;
  address: string;
  status: string; // "Active", "Inactive", "Expired"
  package: string;
  registered: string;
  expiration: string;
  // Add any other properties your API returns, like 'organization' if needed
  organization?: string;
}

type CustomerFilter = "All" | "Active" | "Inactive" | "Expired";

export default function DashboardPage() {
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const router = useRouter(); // Initialize useRouter
  const access_token = Cookies.get("accessToken"); // Get access token

  // State to store the fetched customer data
  const [allCustomers, setAllCustomers] = useState<CustomerType[]>([]);

  // Derived states for counts
  const totalCustomers = allCustomers.length;
  const activeCustomers = allCustomers.filter(
    (customer) => customer.status === "Active"
  ).length;
  const inactiveCustomers = allCustomers.filter(
    (customer) => customer.status === "Inactive"
  ).length;
  const expiredCustomers = allCustomers.filter(
    (customer) => customer.status === "Expired"
  ).length;

  const totalRevenue = "NPR 45,000"; // Placeholder
  const totalInvoices = "150"; // Placeholder
  const pendingInvoices = "20"; // Placeholder
  const openTickets = "25"; // Placeholder

  // Fetch customer data on component mount
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const response = await apiClient.get("/customer", {
          headers: {
            Authorization: `Bearer ${access_token}`,
          },
          cache: "no-store", // Ensure fresh data
        });
        // Assuming your API response data is directly the array of customers
        setAllCustomers(response.data.data);
        console.log("Fetched customers for Dashboard:", response.data.data);
      } catch (error) {
        console.error("Failed to fetch customers for dashboard:", error);
        // Handle error, e.g., show an error message to the user
      }
    };

    if (access_token) {
      fetchCustomers();
    }
  }, [access_token]); // Re-run if access_token changes (though usually it won't on dashboard)

  const handleFilterClick = (filter: CustomerFilter) => {
    setCurrentFilter(filter);
    // Navigate to the /customers page, passing the filter as a query parameter
    router.push(`/app/customers?filter=${filter}`);
  };

  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Dashboard Content */}
      <main className=" flex-1  overflow-auto">
        {/* Client Stats - Top Row with clear status colors */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Total Customers Box */}
          <div
            className={`bg-white text-blue-700 p-4 rounded-lg shadow-md border
              ${
                currentFilter === "All"
                  ? "border-blue-500 ring-2 ring-blue-200"
                  : "border-gray-200 hover:border-blue-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("All")}
          >
            <div>
              <h3 className="text-sm font-medium">Total Customers</h3>
              <p className="text-2xl font-bold">{totalCustomers}</p>
            </div>
            <UsersRound className="h-8 w-8 text-blue-500" />{" "}
            {/* Icon colored to match border */}
          </div>

          {/* Active Clients Box */}
          <div
            className={`bg-white text-green-700 p-4 rounded-lg shadow-md border
              ${
                currentFilter === "Active"
                  ? "border-green-500 ring-2 ring-green-200"
                  : "border-gray-200 hover:border-green-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("Active")}
          >
            <div>
              <h3 className="text-sm font-medium">Active Clients</h3>
              <p className="text-2xl font-bold">{activeCustomers}</p>
            </div>
            <Activity className="h-8 w-8 text-green-500" />{" "}
            {/* Icon colored to match border */}
          </div>

          {/* Inactive Clients Box */}
          <div
            className={`bg-white text-orange-700 p-4 rounded-lg shadow-md border
              ${
                currentFilter === "Inactive"
                  ? "border-orange-500 ring-2 ring-orange-200"
                  : "border-gray-200 hover:border-orange-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("Inactive")}
          >
            <div>
              <h3 className="text-sm font-medium">Inactive Clients</h3>
              <p className="text-2xl font-bold">{inactiveCustomers}</p>
            </div>
            <MinusCircle className="h-8 w-8 text-orange-500" />{" "}
            {/* Icon colored to match border */}
          </div>

          {/* Expired Clients Box */}
          <div
            className={`bg-white text-red-700 p-4 rounded-lg shadow-md border
              ${
                currentFilter === "Expired"
                  ? "border-red-500 ring-2 ring-red-200"
                  : "border-gray-200 hover:border-red-300"
              }
              text-center cursor-pointer transition-all duration-200 flex items-center justify-between`}
            onClick={() => handleFilterClick("Expired")}
          >
            <div>
              <h3 className="text-sm font-medium">Expired Clients</h3>
              <p className="text-2xl font-bold">{expiredCustomers}</p>
            </div>
            <XCircle className="h-8 w-8 text-red-500" />{" "}
            {/* Icon colored to match border */}
          </div>
        </div>

        {/* Metric Cards - Updated to use calculated values */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center">
            <DollarSign className="text-green-500 h-8 w-8 mb-2" />
            <div className="text-xl font-bold">{totalRevenue}</div>
            <div className="text-sm text-gray-500">Monthly Revenue</div>
          </div>

          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center">
            <Link href={"app/invoices"}>
              {" "}
              <Ticket className="text-blue-500 h-8 w-8 mb-2" />
              <div className="text-xl font-bold">{totalInvoices}</div>
              <div className="text-sm text-gray-500">Total Invoices</div>
            </Link>
          </div>

          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center">
            <Activity className="text-yellow-500 h-8 w-8 mb-2" />
            <div className="text-xl font-bold">{pendingInvoices}</div>
            <div className="text-sm text-gray-500">Pending Invoices</div>
          </div>
          <div className="bg-white p-4 rounded shadow flex flex-col items-center justify-center">
            <TrendingUp className="text-red-500 h-8 w-8 mb-2" />
            <div className="text-xl font-bold">{openTickets}</div>
            <div className="text-sm text-gray-500">Open Tickets</div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Revenue Chart</span>
          </div>
          <div className="bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Client Activity</span>
          </div>
          <div className="md:col-span-2 bg-[#3a5f8f] text-white p-4 rounded h-[180px] flex items-center justify-center">
            <span className="text-sm">Customer Demographics Chart</span>
          </div>
        </div>

        {/* Main Panels: Recent Activities & Revenue Chart */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
          {/* Recent Activities */}
          <div className="bg-white p-4 rounded shadow col-span-1 lg:col-span-2">
            <div className="font-bold mb-2">Recent Activities</div>
            <ul className="space-y-2">
              <li className="border-b pb-2 text-sm">
                John Doe signed up (2 hrs ago)
              </li>
              <li className="border-b pb-2 text-sm">
                Invoice #1234 paid (5 hrs ago)
              </li>
              <li className="border-b pb-2 text-sm">
                Ticket #456 updated (1 day ago)
              </li>
              <li className="text-sm">New report generated (2 days ago)</li>
            </ul>
          </div>

          {/* Placeholder Chart Panel */}
          <div className="bg-white p-4 rounded shadow col-span-1">
            <div className="font-bold mb-2">Revenue Chart</div>
            <div className="h-[180px] bg-gray-200 rounded flex items-center justify-center text-gray-500">
              Chart Placeholder
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="bg-white p-4 rounded shadow overflow-x-auto">
          <div className="font-bold mb-2">Client List</div>
          <table className="min-w-full text-sm">
            <thead>
              <tr className="text-left border-b">
                <th className="py-2 px-2">Name</th>
                <th className="py-2 px-2">Email</th>
                <th className="py-2 px-2">Status</th>
              </tr>
            </thead>
            <tbody>
              {/* Display first 3 customers from fetched data or static if empty */}
              {(allCustomers.length > 0
                ? allCustomers
                : [
                    {
                      id: 1,
                      first_name: "John",
                      last_name: "Doe",
                      email: "<EMAIL>",
                      status: "Active",
                    },
                    {
                      id: 2,
                      first_name: "Jane",
                      last_name: "Smith",
                      email: "<EMAIL>",
                      status: "Inactive",
                    },
                    {
                      id: 3,
                      first_name: "Bob",
                      last_name: "Johnson",
                      email: "<EMAIL>",
                      status: "Expired",
                    },
                  ]
              )
                .slice(0, 3)
                .map((customer) => (
                  <tr key={customer.id} className="border-b">
                    <td className="py-2 px-2">
                      {customer.first_name} {customer.last_name}
                    </td>
                    <td className="py-2 px-2">{customer.email}</td>
                    <td
                      className={`py-2 px-2 ${
                        customer.status === "Active"
                          ? "text-green-600"
                          : customer.status === "Inactive"
                          ? "text-yellow-500"
                          : "text-red-500"
                      }`}
                    >
                      {customer.status}
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      </main>
    </div>
  );
}
