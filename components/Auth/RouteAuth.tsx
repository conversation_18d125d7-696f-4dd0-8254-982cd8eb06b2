"use client";
import React from "react";
import { useEffect, useRef } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/context/AuthContext";

interface RouteAuthenticatorProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export function RouteAuthenticator({ children, redirectTo = "/" }: RouteAuthenticatorProps) {
  const { isLoggedIn, isAuthReady } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const hasRedirected = useRef(false);
  const isProtectedRoute = pathname !== "/";

  useEffect(() => {
    if (!isAuthReady || hasRedirected.current) return;

    if (isProtectedRoute && !isLoggedIn) {
      hasRedirected.current = true;
      router.replace(redirectTo);
      return;
    }

    if (pathname === "/" && isLoggedIn) {
      hasRedirected.current = true;
      router.replace("/app");
    }
  }, [isAuthReady, isLoggedIn, pathname, router, redirectTo, isProtectedRoute]);

  if (!isAuthReady) return null;

  const shouldRender = isProtectedRoute ? isLoggedIn : !isLoggedIn;
  return shouldRender ? <>{children}</> : null;
}
