"use client";

import { useAuth } from "@/context/AuthContext";
import { ReactNode } from "react";

interface RequireAuthActionProps {
  groups?: string | string[];
  action?: string;
  children: ReactNode;
  fallback?: ReactNode;
}

export function RequireAuthAction({ groups, action, children, fallback = null }: RequireAuthActionProps) {
  const { user, isLoggedIn } = useAuth();

  const hasRequiredGroup = !groups || !user?.group || 
    (Array.isArray(groups) ? groups.includes(user.group) : user.group === groups);

  const hasRequiredAction = !action || !user?.actions || user.actions.includes(action);

  if (isLoggedIn && hasRequiredGroup && hasRequiredAction) {
    return <>{children}</>;
  }

  return fallback;
}
