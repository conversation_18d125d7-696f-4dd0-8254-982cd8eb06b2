import React from "react";
import { Eye, EyeOff } from "lucide-react";
import { CustomerType } from "@/app/data/customers"; // Adjust path as needed

interface CustomerBasicInfoProps {
  customer: CustomerType;
  showPassword: boolean;
  onTogglePassword: () => void;
}

const CustomerBasicInfo: React.FC<CustomerBasicInfoProps> = ({
  customer,
  showPassword,
  onTogglePassword,
}) => (
  <div className="bg-white p-4 rounded-lg border shadow-sm text-xs border-blue-500">
    <h3 className=" font-semibold mb-2 bg-blue-200 text-blue-900 border-blue-500 border-l-4 p-2 rounded">
      Basic Info
    </h3>
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <tbody className="bg-white divide-y divide-gray-200">
          {/* Customer Identification */}
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Customer Name
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {`${customer.firstName} ${customer.lastName}` || "N/A"}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Username
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.username || "N/A"}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Password
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.password ? (
                <div className="flex items-center">
                  <span className="mr-2">
                    {showPassword ? customer.password : "*********"}
                  </span>
                  <button
                    onClick={onTogglePassword}
                    className="text-gray-500 hover:text-gray-700 focus:outline-none"
                    aria-label={
                      showPassword ? "Hide password" : "Show password"
                    }
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              ) : (
                "N/A"
              )}
            </td>
          </tr>
          {/* Contact Information */}
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Mobile
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.mobile || "N/A"}
            </td>
          </tr>
          {customer.altMobile && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Alt. Mobile
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.altMobile || "N/A"}
              </td>
            </tr>
          )}
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Email
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.email || "N/A"}
            </td>
          </tr>
          {customer.dateOfBirth && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                DOB
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.dateOfBirth || "N/A"}
              </td>
            </tr>
          )}
          {customer.nationalId && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                National ID
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.nationalId || "N/A"}
              </td>
            </tr>
          )}
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Branch
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.branch || "N/A"}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Customer Type
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.customerType || "N/A"}
            </td>
          </tr>

          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Organization
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.organization || "N/A"}
            </td>
          </tr>

          {/* Location & Network Details */}
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Geo Location
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.geoLocation
                ? (() => {
                    const [latitude, longitude] = customer.geoLocation
                      .split(",")
                      .map((coord) => coord.trim());
                    return (
                      <a
                        href={`https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        Location (Click Here)
                      </a>
                    );
                  })()
                : "N/A"}
            </td>
          </tr>
         
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Registered
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.registered || "N/A"}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
);

export default CustomerBasicInfo;
