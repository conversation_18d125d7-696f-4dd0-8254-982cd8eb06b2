import React from 'react';
import { Button } from "@/components/ui/button"; // Assuming this path is correct
import { UsageRecord } from "@/app/data/customers"; // Adjust path as needed

interface CustomerUsageSectionProps {
  startDate: string;
  setStartDate: (date: string) => void;
  endDate: string;
  setEndDate: (date: string) => void;
  handleDateSubmit: () => void;
  filteredUsageByDate: UsageRecord[];
}

const CustomerUsageSection: React.FC<CustomerUsageSectionProps> = ({
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  handleDateSubmit,
  filteredUsageByDate,
}) => {
  return (
    <div className="space-y-4">
      {/* Date Range Selector */}
      <div className="flex flex-col sm:flex-row items-center justify-center sm:justify-start space-y-2 sm:space-y-0 sm:space-x-4 bg-white p-0 rounded-lg shadow-sm">
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="p-2 border h-8 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-auto text-sm"
          />
          <span className="flex items-center justify-center sm:justify-start text-gray-700">
            -
          </span>
          <input
            type="date"
            value={endDate}
            onChange={(e) => setEndDate(e.target.value)}
            className="p-2 border h-8 border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-auto text-sm"
          />
        </div>
        <Button
          size="sm"
          onClick={handleDateSubmit}
          className="px-4 py-2 w-full sm:w-auto text-sm"
        >
          Submit
        </Button>
      </div>

      {/* Usage Table */}
      <div className="overflow-x-auto bg-white rounded-lg shadow-md">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Start Time
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                End Time
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Session Time
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Upload
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Download
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                IP Address
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                MAC Address
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
              >
                Service Type
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredUsageByDate.length > 0 ? (
              filteredUsageByDate.map((row, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.startTime}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.endTime}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.sessionTime}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.upload}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.download}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.ipAddress}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.macAddress}
                  </td>
                  <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                    {row.serviceType}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={8}
                  className="px-3 py-4 text-center text-gray-500"
                >
                  No usage data found for the selected customer or date range.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CustomerUsageSection;