import React from 'react';
import { Button } from "@/components/ui/button"; // Assuming this path is correct
import RandomLineGraph from "@/components/random-line-graph"; // Adjust path as needed

interface GraphRange {
  label: string;
  value: string;
}

interface CustomerGraphSectionProps {
  selectedGraphRange: string;
  setSelectedGraphRange: (range: string) => void;
  graphRanges: GraphRange[]; // Pass the array of ranges
}

const CustomerGraphSection: React.FC<CustomerGraphSectionProps> = ({
  selectedGraphRange,
  setSelectedGraphRange,
  graphRanges,
}) => {
  return (
    <div className="bg-white shadow-md rounded-lg p-5 space-y-4">
      <h3 className="text-lg font-semibold">Usage Graph</h3>
      {/* Graph Range Selector Buttons */}
      <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
        {graphRanges.map((range) => (
          <Button
            key={range.value}
            size="sm"
            variant={
              selectedGraphRange === range.value ? "default" : "outline"
            }
            onClick={() => setSelectedGraphRange(range.value)}
            className="text-xs"
          >
            {range.label}
          </Button>
        ))}
      </div>
      {/* Render the RandomLineGraph component, passing the selected range */}
      <RandomLineGraph timeRange={selectedGraphRange} />
    </div>
  );
};

export default CustomerGraphSection;