"use client";

import React from "react";
import { useState, useRef, useEffect } from "react";
import { Settings2, X } from "lucide-react";
import { useSearchParams } from "next/navigation"; // Correct import for App Router
import {
  ListRestart,
  UserAdd,
  UserPen,
  SquarePen,
  ChevronRight,
  ChevronLeft,
} from "@/components/icons/list"; // Make sure to import X icon if you use it for close button
import ISPCustomerForm from "@/components/customer/isp-customer-form";
import CustomerView from "@/components/view-customer";
import CustomerEdit from "@/components/customer/customer-edit"; // Import the CustomerEdit component
import { Button } from "@/components/ui/button";
// import { customers } from "@/app/data/customers"; // Import customers
import Cookies from "js-cookie";
import jwt from "jsonwebtoken";
import apiClient from "@/lib/apiClient";

// Define possible filter states
type CustomerFilter = "All" | "Active" | "Inactive" | "Expired";

// Define ALL available columns and their default visibility
const initialVisibleColumns = {
  sn: true,
  status: true,
  username: true,
  name: true,
  contact: true,
  address: true,
  valid_till: true,
  package: true,
  action: true,
  mac: false,
  customerType: false,
  registered: false,
  vlanId: false,
  lastActive: false,
  lastPaymentDate: false,
};

interface Customer {
  id: number;
  first_name: string;
  last_name: string;
  username: string;
  password: string;
  contact: string[];
  email: string;
  address: string;
  status: string;
  package: string;
  registered: string;
  expiration: string;
}

interface Organization {
  id: string;
  name: string;
}

export default function CustomersDetails() {
  const [showForm, setShowForm] = useState(false); // true if ADD form or EDIT form is active
  const [selectedCustomer, setSelectedCustomer] = useState<any | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentFilter, setCurrentFilter] = useState<CustomerFilter>("All");
  const [selectedItemsPerPageValue, setSelectedItemsPerPageValue] =
    useState<string>("15");
  const [showColumnFilterPopup, setShowColumnFilterPopup] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState(initialVisibleColumns);
  const [selectedOrgId, setSelectedOrgId] = useState<string>("");

  // Create a ref for the top-level div of THIS component
  const componentRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const [customer, setCustomer] = useState<Customer[]>([]);
  const [organization, setOrganization] = useState<Organization[]>([]);

  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        const access_token = Cookies.get("accessToken");
        const payload = jwt.decode(access_token) as any;
        const orgId = payload.org_id;
        const response = await apiClient.get(`/customer/`);
        setCustomer(response.data.data);
        console.log(response.data.data);
      } catch (error) {
        console.error("Failed to fetch customer:", error);
      }
    };

    fetchCustomer();
  }, []);

  useEffect(() => {
    const fetchOrganization = async () => {
      try {
        const response = await apiClient.get("/organization");
        setOrganization(response.data.data);
        console.log(response.data.data);
      } catch (error) {
        console.error("Failed to fetch organization:", error);
      }
    };

    fetchOrganization();
  }, []);

  const handleOrganizationChange = async (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const orgId = e.target.value;
    setSelectedOrgId(orgId);

    if (orgId) {
      try {
        const response = await apiClient.get(`/customer/?org_id=${orgId}`);
        setCustomer(response.data.data);
      } catch (error) {
        console.error("Failed to fetch customers by org:", error);
      }
    } else {
      // If no org selected, fetch all customers
      try {
        const response = await apiClient.get(`/customer/`);
        setCustomer(response.data.data);
      } catch (error) {
        console.error("Failed to fetch all customers:", error);
      }
    }
  };

  // Effect to read the filter from the URL on component mount
  useEffect(() => {
    const filterFromUrl = searchParams.get("filter") as CustomerFilter;
    if (
      filterFromUrl &&
      ["All", "Active", "Inactive", "Expired"].includes(filterFromUrl)
    ) {
      setCurrentFilter(filterFromUrl);
      setCurrentPage(1); // Reset page to 1 when filter changes from URL
    }
  }, [searchParams]); // Re-run if searchParams change

  const itemsPerPage =
    selectedItemsPerPageValue === "all"
      ? customer.length
      : parseInt(selectedItemsPerPageValue, 10);

  const filteredCustomers = customer.filter((customer) => {
    if (currentFilter === "All") {
      return true;
    }
    return customer.status === currentFilter;
  });

  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    const usernameA = a.username.toLowerCase();
    const usernameB = b.username.toLowerCase();
    if (usernameA < usernameB) {
      return -1;
    }
    if (usernameA > usernameB) {
      return 1;
    }
    return 0;
  });

  const totalPages =
    itemsPerPage === sortedCustomers.length
      ? 1
      : Math.ceil(sortedCustomers.length / itemsPerPage);

  const currentCustomers = sortedCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  /**
   * Helper function to scroll the closest scrollable parent to the top.
   * This is more reliable when the component itself isn't the direct scroll container.
   */
  const scrollToTop = () => {
    if (componentRef.current) {
      // Find the closest parent with overflow-y: auto or scroll
      let currentElement: HTMLElement | null = componentRef.current;
      while (currentElement && currentElement !== document.body) {
        const style = window.getComputedStyle(currentElement);
        // Ensure the element has scrollable content and is visually scrollable
        if (
          (style.overflowY === "auto" || style.overflowY === "scroll") &&
          currentElement.scrollHeight > currentElement.clientHeight
        ) {
          currentElement.scrollTop = 0;
          return; // Found and scrolled the container
        }
        currentElement = currentElement.parentElement;
      }
      // Fallback to window scroll if no specific scrollable parent is found within the component's hierarchy
      // This is less ideal for component-specific scrolling, but works as a fallback.
      window.scrollTo({ top: 0, behavior: "smooth" }); // Added smooth behavior
    }
  };

  useEffect(() => {
    scrollToTop();
  }, [currentPage]);

  // Handles toggling the "Add Customer" form
  const handleAddCustomerToggle = () => {
    setShowForm(!showForm);
    setSelectedCustomer(null); // Ensure no customer is selected when adding
    setShowColumnFilterPopup(false); // Hide column filter popup when adding customer
    scrollToTop(); // Scroll to top when toggling mode
  };

  // Handles selecting a customer for VIEWING details (e.g., clicking username)
  const handleSelectCustomer = (customer: any) => {
    setSelectedCustomer(customer);
    setShowForm(false); // Hide any forms when a customer is selected for view
    setShowColumnFilterPopup(false); // Hide column filter popup when viewing customer
    scrollToTop(); // Scroll to top when a customer is selected
  };

  // Handles selecting a customer for EDITING (e.g., clicking edit icon)
  const handleEditCustomer = (customer: any) => {
    setSelectedCustomer(customer); // Set the customer to be edited
    setShowForm(true); // Show the form, which will now be the edit form
    setShowColumnFilterPopup(false); // Hide column filter popup when editing customer
    scrollToTop();
  };

  // Handles going back to the customer list from any view/form
  const handleBackToList = () => {
    setSelectedCustomer(null);
    setShowForm(false);
    setShowColumnFilterPopup(false); // Hide column filter popup when going back to list
    scrollToTop(); // Scroll to top when going back to list view
  };

  const handleFilterClick = (filter: CustomerFilter) => {
    setCurrentFilter(filter);
    setCurrentPage(1);
    setSelectedCustomer(null); // Clear selected customer when applying filter
    setShowForm(false); // Hide any forms when applying filter
    setShowColumnFilterPopup(false); // Hide column filter popup when applying filter
    //scrollToTop(); // Scroll to top when applying a new filter
  };

  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setSelectedItemsPerPageValue(value);
    setCurrentPage(1);
    //scrollToTop(); // Scroll to top when changing items per page
  };

  // New handler for toggling column visibility
  const handleColumnToggle = (
    columnName: keyof typeof initialVisibleColumns
  ) => {
    setVisibleColumns((prev) => ({
      ...prev,
      [columnName]: !prev[columnName],
    }));
  };

  // Helper to get user-friendly column names
  const getColumnName = (key: string) => {
    switch (key) {
      case "sn":
        return "SN";
      case "valid_till":
        return "Valid Till";
      case "customerType":
        return "Customer Type";
      case "registered":
        return "Register Date";
      case "lastActive":
        return "Last Active";
      case "lastPaymentDate":
        return "Last Renew";
      case "mac":
        return "MAC";
      default:
        return key.charAt(0).toUpperCase() + key.slice(1); // Capitalize first letter
    }
  };

  return (
    <div
      ref={componentRef}
      className="mx-auto bg-white rounded-lg p-5 sm:p-6 lg:p-5 max-w-full"
    >
      {/* Header and Add Customer Button */}
      {/* Only show header if no form or customer view is active */}
      {!showForm && !selectedCustomer && (
        <div className="flex flex-col sm:flex-row items-center justify-between gap-3 mb-3">
          <h1 className="text-x sm:text-x font-bold text-center sm:text-left w-full sm:w-auto">
            ISP Customers
          </h1>
          <Button
            size="sm"
            className="w-full sm:w-auto"
            onClick={handleAddCustomerToggle}
          >
            <UserAdd /> Add Customer
          </Button>
        </div>
      )}

      {/* Conditional Rendering of Forms/Views */}
      {showForm ? ( // If `showForm` is true, either an add form or edit form is active
        selectedCustomer ? ( // If `selectedCustomer` is not null, it's an EDIT operation
          <div className=" p-2 sm:p-0">
            <CustomerEdit
              customer={selectedCustomer}
              onCancel={handleBackToList}
            />
          </div>
        ) : (
          // If `selectedCustomer` is null, it's an ADD operation
          <div className=" p-2 sm:p-0">
            {/* Pass handleBackToList as both onFormSuccess and onCancel */}
            <ISPCustomerForm
              onFormSuccess={handleBackToList}
              onCancel={handleBackToList}
            />
          </div>
        )
      ) : selectedCustomer ? ( // If `showForm` is false, but `selectedCustomer` is not null, it's a VIEW operation
        <div>
          <CustomerView
            selectedCustomer={selectedCustomer}
            setSelectedCustomer={handleBackToList} // Use handleBackToList to go back
          />
        </div>
      ) : (
        // Default state: Show the customer list and filters
        <>
          <div className="w-full mb-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "All"
                    ? "border-blue-500 ring-2 ring-blue-200"
                    : "border-blue-200 hover:border-blue-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("All")}
              >
                <h3 className="text-xs font-medium text-blue-700 mb-0.5">
                  Total Customers
                </h3>
                <p className="text-xl font-bold text-blue-800">
                  {customer.length}
                </p>
              </div>

              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "Active"
                    ? "border-green-500 ring-2 ring-green-200"
                    : "border-green-200 hover:border-green-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("Active")}
              >
                <h3 className="text-xs font-medium text-green-700 mb-0.5">
                  Active Customers
                </h3>
                <p className="text-xl font-bold text-green-800">
                  {customer.filter((c) => c.status === "Active").length}
                </p>
              </div>

              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "Inactive"
                    ? "border-yellow-500 ring-2 ring-yellow-200"
                    : "border-yellow-200 hover:border-yellow-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("Inactive")}
              >
                <h3 className="text-xs font-medium text-yellow-700 mb-0.5">
                  Inactive Customers
                </h3>
                <p className="text-xl font-bold text-yellow-800">
                  {customer.filter((c) => c.status === "Inactive").length}
                </p>
              </div>

              <div
                className={`bg-white px-4 py-1 rounded-lg shadow-md border ${
                  currentFilter === "Expired"
                    ? "border-red-500 ring-2 ring-red-200"
                    : "border-red-200 hover:border-red-300"
                } text-center cursor-pointer transition-all duration-200`}
                onClick={() => handleFilterClick("Expired")}
              >
                <h3 className="text-xs font-medium text-red-700 mb-0.5">
                  Expired Customers
                </h3>
                <p className="text-xl font-bold text-red-800">
                  {customer.filter((c) => c.status === "Expired").length}
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-4">
            <div className="flex items-center gap-2 text-sm">
              <span>Show</span>
              <select
                value={selectedItemsPerPageValue}
                onChange={handleItemsPerPageChange}
                className="border border-gray-300 rounded-md p-1.5 h-7 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="15">15</option>
                <option value="30">30</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="all">All</option>
              </select>
              <span>entries</span>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <span>Organization : </span>
              <select
                value={selectedOrgId}
                onChange={handleOrganizationChange}
                className="border border-gray-300 rounded-md p-1.5 h-7 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All</option>
                {organization.map((org) => (
                  <option key={org.id} value={org.id}>
                    {org.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Column Filter Button (opens popup) */}
            <div>
              <Button
                size="sm"
                variant="outline"
                className="flex items-center gap-1"
                onClick={() => setShowColumnFilterPopup(true)} // Open popup
              >
                <Settings2 className="h-4 w-4" /> Column Filter
              </Button>
            </div>
          </div>

          <div className="w-full overflow-x-auto rounded shadow-sm">
            <table className="min-w-max w-full text-xs sm:text-[12px]">
              <thead className="bg-gray-100 text-left">
                <tr>
                  {visibleColumns.sn && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                      SN
                    </th>
                  )}
                  {visibleColumns.status && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                      Status
                    </th>
                  )}
                  {visibleColumns.username && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                      Username
                    </th>
                  )}
                  {visibleColumns.name && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                      Name
                    </th>
                  )}
                  {visibleColumns.contact && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2">
                      contact
                    </th>
                  )}
                  {visibleColumns.address && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden sm:table-cell">
                      Address
                    </th>
                  )}
                  {visibleColumns.valid_till && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Valid Till
                    </th>
                  )}
                  {visibleColumns.package && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Package
                    </th>
                  )}
                  {visibleColumns.mac && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      MAC
                    </th>
                  )}
                  {visibleColumns.customerType && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Customer Type
                    </th>
                  )}
                  {visibleColumns.registered && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Register Date
                    </th>
                  )}
                  {visibleColumns.vlanId && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Vlan
                    </th>
                  )}
                  {visibleColumns.lastActive && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Last Active
                    </th>
                  )}
                  {visibleColumns.lastPaymentDate && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Last Renew
                    </th>
                  )}
                  {visibleColumns.action && (
                    <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                      Action
                    </th>
                  )}
                </tr>
              </thead>
              <tbody>
                {currentCustomers.map((customer, index) => (
                  <tr
                    key={customer.id}
                    className={`hover:bg-gray-50 ${
                      selectedCustomer?.id === customer.id ? "bg-blue-50" : ""
                    }`}
                  >
                    {visibleColumns.sn && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 text-center">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </td>
                    )}
                    {visibleColumns.status && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5">
                        {customer.status === "Active" && (
                          <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded-l-1 bg-green-100 text-green-900">
                            Active
                          </span>
                        )}
                        {customer.status === "Inactive" && (
                          <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded-l-1 bg-yellow-100 text-yellow-900">
                            Inactive
                          </span>
                        )}
                        {customer.status === "Expired" && (
                          <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded-l-1 bg-red-100 text-red-900">
                            Expired
                          </span>
                        )}
                      </td>
                    )}
                    {visibleColumns.username && (
                      <td
                        className="border-b border-gray-300 px-1 sm:px-2 py-0.5 text-blue-600 hover:underline cursor-pointer"
                        onClick={() => handleSelectCustomer(customer)} // For viewing details
                      >
                        {customer.username}
                      </td>
                    )}
                    {visibleColumns.name && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5">
                        {customer.first_name} {customer.last_name}
                      </td>
                    )}
                    {visibleColumns.contact && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5">
                        {customer.contact}
                      </td>
                    )}
                    {visibleColumns.address && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden sm:table-cell">
                        {customer.address}
                      </td>
                    )}
                    {visibleColumns.valid_till && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                        {customer.valid_till || "N/A"}
                      </td>
                    )}
                    {visibleColumns.package && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 truncate hidden md:table-cell">
                        {customer.pack_id || "N/A"}
                      </td>
                    )}
                    {visibleColumns.mac && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                        {customer.mac || "N/A"}
                      </td>
                    )}
                    {visibleColumns.customerType && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                        {customer.customerType || "N/A"}
                      </td>
                    )}
                    {visibleColumns.registered && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                        {customer.registered || "N/A"}
                      </td>
                    )}
                    {visibleColumns.vlanId && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                        {customer.routerInfo?.vlanId || "N/A"}
                      </td>
                    )}
                    {visibleColumns.lastActive && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                        {customer.lastActive || "N/A"}
                      </td>
                    )}
                    {visibleColumns.lastPaymentDate && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 hidden md:table-cell">
                        {customer.lastPaymentDate || "N/A"}
                      </td>
                    )}
                    {visibleColumns.action && (
                      <td className="border-b border-gray-300 px-1 sm:px-2 py-0.5 text-center hidden md:table-cell">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-7 w-7"
                          onClick={() => handleEditCustomer(customer)} // Call the new edit handler
                        >
                          <SquarePen />
                        </Button>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {totalPages > 1 && (
            <div className="flex items-center gap-1 justify-left mt-4">
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() => setCurrentPage((p) => Math.max(p - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft />
              </Button>
              <span className="text-[12px]">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                className="rounded-full w-8 h-6"
                size="ss"
                onClick={() =>
                  setCurrentPage((p) => Math.min(p + 1, totalPages))
                }
                disabled={currentPage === totalPages}
              >
                <ChevronRight />
              </Button>
            </div>
          )}
        </>
      )}

      {/* Column Filter Popup */}
      {showColumnFilterPopup && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-sm relative">
            <div className="flex justify-between items-center mb-4 border-b pb-2">
              <h3 className="text-lg font-semibold text-gray-800">
                Filter Columns
              </h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowColumnFilterPopup(false)} // Close popup
                className="rounded-full h-8 w-8"
              >
                <X className="h-4 w-4 text-gray-500 hover:text-gray-700" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-y-3 gap-x-4">
              {Object.entries(initialVisibleColumns).map(([key, value]) => (
                <div key={key} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`column-popup-${key}`} // Unique ID for popup checkboxes
                    checked={
                      visibleColumns[key as keyof typeof initialVisibleColumns]
                    }
                    onChange={() =>
                      handleColumnToggle(
                        key as keyof typeof initialVisibleColumns
                      )
                    }
                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor={`column-popup-${key}`}
                    className="text-sm capitalize text-gray-700"
                  >
                    {getColumnName(key)} {/* Use helper for display name */}
                  </label>
                </div>
              ))}
            </div>
            <div className="mt-6 flex justify-end gap-3">
              <Button
                variant="outline"
                onClick={() => setShowColumnFilterPopup(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
