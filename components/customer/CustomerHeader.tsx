import React from 'react';
import { Button } from '@/components/ui/button'; // Assuming you use this Button component
import { UserPen } from '@/components/icons/list'; // Assuming this icon is for edit

interface CustomerHeaderProps {
  firstName: string;
  lastName: string;
  status: string;
  address: string;
}

const CustomerHeader: React.FC<CustomerHeaderProps> = ({ firstName, lastName, status, address}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 items-center p-2 rounded-lg text-sm gap-2">
      <div className="font-semibold text-center md:text-left">
        {firstName} { lastName}
      </div>
      <div className="text-center">
        Status:{" "}
        <span
          className={`font-medium ${
            status === "Active"
              ? "text-green-600"
              : status === "Inactive"
              ? "text-yellow-600"
              : "text-red-600"
          }`}
        >
          {status}
        </span>
      </div>
      <div className="text-center md:text-right text-gray-700 flex flex-col md:flex-row items-center md:justify-end gap-2">
        <span>Address: {address}</span>
      </div>
    </div>
  );
};

export default CustomerHeader;