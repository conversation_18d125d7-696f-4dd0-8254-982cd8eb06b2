import React from "react";
import { CustomerType } from "@/app/data/customers"; // Adjust path as needed

interface CustomerServicePackageInfoProps {
  customer: CustomerType;
}

const CustomerServicePackageInfo: React.FC<CustomerServicePackageInfoProps> = ({
  customer,
}) => (
  <div className="bg-white p-4 rounded-lg border shadow-sm text-xs border-green-500">
    <h3 className="font-semibold mb-2 bg-green-200 text-green-900 border-green-300 border-l-4 p-2 rounded">
      Service & Package Info
    </h3>
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <tbody className="bg-white divide-y divide-gray-200">
           <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Service
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.service}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Package
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.package}
            </td>
          </tr>
          {customer.bandwidthDownload && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Download
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.bandwidthDownload}
              </td>
            </tr>
          )}
          {customer.bandwidthUpload && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Upload
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.bandwidthUpload}
              </td>
            </tr>
          )}
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Package Period
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.packagePeriod}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Expiration Date
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.expiration}
            </td>
          </tr>
        
          {customer.lastPaymentDate && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Last Paid
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.lastPaymentDate}
              </td>
            </tr>
          )}
          {customer.nextBillDueDate && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Next Bill
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.nextBillDueDate}
              </td>
            </tr>
          )}
          {customer.outstandingBalance && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Outstanding
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.outstandingBalance}
              </td>
            </tr>
          )}
           <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Apply FUP
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.applyFUP === "Yes" ? (
                <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800">
                  Yes
                </span>
              ) : customer.applyFUP === "No" ? (
                <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                  No
                </span>
              ) : (
                <span>{customer.applyFUP || "N/A"}</span>
              )}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
);

export default CustomerServicePackageInfo;
