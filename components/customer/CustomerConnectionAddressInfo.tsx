import React from "react";
import { CustomerType } from "@/app/data/customers"; // Adjust path as needed

interface CustomerConnectionAddressInfoProps {
  customer: CustomerType;
}

const CustomerConnectionAddressInfo: React.FC<
  CustomerConnectionAddressInfoProps
> = ({ customer }) => (
  <div className="bg-white p-4 rounded-lg border shadow-sm text-xs border-yellow-500">
    <h3 className="font-semibold mb-2 bg-yellow-200 text-yellow-900 border-yellow-300 border-l-4 p-2 rounded">
      {/* <h3 className="font-semibold mb-2 bg-[#0e1e2e] p-2 rounded text-white"> */}
      Connection Info
    </h3>
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <tbody className="bg-white divide-y divide-gray-200">
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Service Address
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.address}
            </td>
          </tr>
          {customer.landmark && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Landmark
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.landmark}
              </td>
            </tr>
          )}
          {customer.ip && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                Assigned IP
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.ip}
              </td>
            </tr>
          )}
          {customer.mac && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                CPE MAC
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.mac}
              </td>
            </tr>
          )}
          {customer.nasIp && (
            <tr>
              <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
                NAS IP
              </td>
              <td className="px-2 py-1 whitespace-nowrap text-gray-900">
                {customer.nasIp}
              </td>
            </tr>
          )}
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Session Status:
            </td>
            <td className="px-2 py-1 whitespace-nowrap">
              {customer.status === "Expired" ? (
                <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                  Expired
                </span>
              ) : customer.sessionStatus === "Online" ? (
                <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-green-100 text-green-800">
                  Online
                </span>
              ) : customer.sessionStatus === "Offline" ? (
                <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-red-100 text-red-800">
                  Offline
                </span>
              ) : (
                <span className="inline-flex items-center gap-1 px-2 py-0.5 text-xs font-semibold rounded bg-gray-100 text-gray-800">
                  {customer.sessionStatus || "-"}
                </span>
              )}
            </td>
          </tr>
          <tr>
            <td className="px-2 py-1 whitespace-nowrap font-medium text-gray-900">
              Online Duration
            </td>
            <td className="px-2 py-1 whitespace-nowrap text-gray-900">
              {customer.onlineDuration || "-"}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
);

export default CustomerConnectionAddressInfo;
