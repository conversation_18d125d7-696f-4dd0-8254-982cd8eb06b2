import React from 'react';
import { UsageRecord } from '@/app/data/customers'; // Adjust path as needed

interface CustomerRecentUsageProps {
  usageRecords: UsageRecord[];
}

const CustomerRecentUsage: React.FC<CustomerRecentUsageProps> = ({
  usageRecords,
}) => (
  <div className="bg-white p-4 rounded-lg border shadow-sm text-xs overflow-x-auto border-gray-300">
    <h3 className="font-semibold mb-3 text-sm">Last 3 Sessions</h3>
    {usageRecords.length > 0 ? (
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-100">
          <tr>
            <th
              scope="col"
              className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
            >
              Start Time
            </th>
            <th
              scope="col"
              className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
            >
              End Time
            </th>
            <th
              scope="col"
              className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
            >
              Session
            </th>
            <th
              scope="col"
              className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
            >
              Download
            </th>
            <th
              scope="col"
              className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
            >
              Upload
            </th>
            <th
              scope="col"
              className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
            >
              IP Address
            </th>
            <th
              scope="col"
              className="px-3 py-2 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
            >
              MAC Address
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {usageRecords.map((record, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                {record.startTime}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                {record.endTime}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                {record.sessionTime}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                {record.download}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                {record.upload}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                {record.ipAddress}
              </td>
              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-800">
                {record.macAddress}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    ) : (
      <p className="text-gray-500">No recent usage sessions found.</p>
    )}
  </div>
);

export default CustomerRecentUsage;