"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface CustomerEditFormProps {
  customer: any; // Consider a more specific type if possible
  onCancel: () => void;
  onSave?: (updatedCustomerData: any) => void;
}

const provinceDistrictMap: Record<string, string[]> = {
  Bagmati: [
    "Bhaktapur",
    "Chitwan",
    "Dhading",
    "Dolakha",
    "Kathmandu",
    "Kavrepalanchok (Kavre)",
    "Lalitpur",
    "Makwanpur",
    "Nuwakot",
    "Ramechhap",
    "Rasuwa",
    "Sindhuli",
    "Sindhupalchok",
  ],
  Gandaki: [
    "Baglung",
    "Gorkha",
    "Kaski",
    "Lamjung",
    "Manang",
    "Mustang",
    "Myagdi",
    "Nawalpur",
    "Parbat",
    "Syangja",
    "Tanahun",
  ],
  Lumbini: [
    "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "Eastern Rukum",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "Rupandehi",
  ],
};

const districtMunicipalityMap: Record<string, string[]> = {
  Kathmandu: [
    "Kathmandu Metropolitan City",
    "Kageshwori-Manohara",
    "Gokarneshwor",
    "Budhanilkantha",
    "Tokha",
    "Tarakeshwar",
    "Nagarjun",
    "Chandragiri",
    "Dakshinkali",
    "Shankharapur",
  ],
  Lalitpur: [
    "Lalitpur Metropolitan City",
    "Mahalaxmi",
    "Godawari",
    "Konjyosom",
    "Bagmati",
    "Mahankal",
  ],
  Bhaktapur: ["Bhaktapur", "Madhyapur Thimi", "Changunarayan", "Suryabinayak"],
};

const getTodayDate = () => {
  const today = new Date();
  return today.toISOString().split("T")[0]; // Format: YYYY-MM-DD
};

export default function CustomerEditForm({
  customer,
  onCancel,
  onSave,
}: CustomerEditFormProps) {
  const [form, setForm] = useState({
    organization: "",
    service: "",
    package: "",
    packagePeriod: "",
    branch: "",
    firstName: "",
    lastName: "",
    username: "",
    customerType: "", // Will map from customer.status
    mobile: "",
    email: "",
    province: "",
    district: "",
    municipality: "",
    ward: "",
    tole: "",
    address: "",
    idType: "",
    idNumber: "", // Will map from customer.nationalId
    registerDate: getTodayDate(), // Will map from customer.registered
  });

  // Initialize form state with customer data when the component mounts or customer prop changes.
  useEffect(() => {
    if (customer) {
      setForm((prevForm) => ({
        ...prevForm, // Keep existing state for fields not directly overridden
        organization: customer.organization || "",
        service: customer.service || "",
        package: customer.package || "",
        packagePeriod: customer.packagePeriod || "", // Mapping from contractType
        branch: customer.branch || "",
        firstName: customer.firstName || "",
        lastName: customer.lastName || "",
        username: customer.username || "",
        customerType: customer.customerType || "", // Mapping from status
        mobile: customer.mobile || "",
        email: customer.email || "",
        province: customer.province || "",
        district: customer.district || "",
        municipality: customer.municipality || "",
        ward: customer.ward || "",
        tole: customer.tole || "",
        address: customer.address || "",
        idType: customer.idType || "",
        idNumber: customer.nationalId || "", // Mapping from nationalId
        registerDate: customer.registered || getTodayDate(), // Mapping from registered
      }));
    }
  }, [customer]);

  const handleChange = (field: keyof typeof form, value: string) => {
    if (field === "province") {
      setForm((prevForm) => ({
        ...prevForm,
        province: value,
        district: "",
        municipality: "",
      }));
    } else if (field === "district") {
      setForm((prevForm) => ({
        ...prevForm,
        district: value,
        municipality: "",
      }));
    } else {
      setForm((prevForm) => ({ ...prevForm, [field]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Updated Customer Data:", form);
    // In a real application, send this 'form' data to your backend API.
    // After successful submission, you would typically call onSave(form) or onCancel()
    if (onSave) {
      onSave(form);
    } else {
      onCancel();
    }
  };

  return (
    <div className="bg-white p-5 rounded shadow mx-auto space-y-5">
      <h2 className="text-lg font-semibold mb-4">
        Edit Customer: {form.username}
      </h2>
      <form onSubmit={handleSubmit}>
        <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="organization">Organization Name</Label>
            <select
              id="organization"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.organization}
              onChange={(e) => handleChange("organization", e.target.value)}
            >
              <option value="">Select</option>
              <option value="Workalaya">Workalaya</option>
              <option value="Sky Broadband">Sky Broadband</option>
              <option value="CNC">CNC</option>
            </select>
          </div>

          <div>
            <Label htmlFor="service">Service</Label>
            <select
              id="service"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.service}
              onChange={(e) => handleChange("service", e.target.value)}
            >
              <option value="">Select</option>
              <option value="Dedicated-Line">Dedicated Line</option>
              <option value="FTTH">FTTH</option>
              <option value="SOHO">SOHO</option>
              <option value="IPtv">IPTV</option>
            </select>
          </div>

          <div>
            <Label htmlFor="package">Package</Label>
            <select
              id="package"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.package}
              onChange={(e) => handleChange("package", e.target.value)}
            >
              <option value="">Select</option>
              <option value="50Mbps">50Mbps</option>
              <option value="100Mbps">100Mbps</option>
              <option value="150Mbps">150Mbps</option>
              <option value="200Mbps">200Mbps</option>
            </select>
          </div>

          <div>
            <Label htmlFor="packagePeriod">Package Period</Label>
            <select
              id="packagePeriod"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.packagePeriod}
              onChange={(e) => handleChange("packagePeriod", e.target.value)}
            >
              <option value="">Select</option>
              <option value="1 Month">1 Month</option>
              <option value="3 Months">3 Months</option>
              <option value="6 Months">6 Months</option>
              <option value="12 Months">12 Months</option>
            </select>
          </div>

          <div>
            <Label htmlFor="branch">Branch</Label>
            <select
              id="branch"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.branch}
              onChange={(e) => handleChange("branch", e.target.value)}
            >
              <option value="">Select</option>
              <option value="Kapan">Kapan</option>
              <option value="Koteshwor">Koteshwor</option>
              <option value="Tinkune">Tinkune</option>
              <option value="Lalitpur">Lalitpur</option>
              <option value="Chabahil">Chabahil</option>
            </select>
          </div>

          <div>
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              type="text"
              value={form.firstName}
              onChange={(e) => handleChange("firstName", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              type="text"
              value={form.lastName}
              onChange={(e) => handleChange("lastName", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              type="text"
              value={form.username}
              onChange={(e) => handleChange("username", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="customerType">Customer Type</Label>
            <select
              id="customerType"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.customerType}
              onChange={(e) => handleChange("customerType", e.target.value)}
            >
              <option value="">Select</option>
              <option value="Home">Home</option>
              <option value="Business">Business</option>
            </select>
          </div>

          <div>
            <Label htmlFor="mobile">Mobile Number</Label>
            <Input
              id="mobile"
              type="tel"
              value={form.mobile}
              onChange={(e) => handleChange("mobile", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={form.email}
              onChange={(e) => handleChange("email", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="province">Province</Label>
            <select
              id="province"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.province}
              onChange={(e) => handleChange("province", e.target.value)}
            >
              <option value="">Select Province</option>
              {Object.keys(provinceDistrictMap).map((province) => (
                <option key={province} value={province}>
                  {province}
                </option>
              ))}
            </select>
          </div>

          <div>
            <Label htmlFor="district">District</Label>
            <select
              id="district"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.district}
              onChange={(e) => handleChange("district", e.target.value)}
              disabled={!form.province}
            >
              <option value="">Select District</option>
              {form.province &&
                provinceDistrictMap[form.province].map((district) => (
                  <option key={district} value={district}>
                    {district}
                  </option>
                ))}
            </select>
          </div>

          <div>
            <Label htmlFor="municipality">
              Municipality / Rural Municipality
            </Label>
            <select
              id="municipality"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.municipality}
              onChange={(e) => handleChange("municipality", e.target.value)}
              disabled={
                !form.district || !districtMunicipalityMap[form.district]
              }
            >
              <option value="">Select Municipality</option>
              {form.district &&
                districtMunicipalityMap[form.district]?.map((mun) => (
                  <option key={mun} value={mun}>
                    {mun}
                  </option>
                ))}
            </select>
          </div>

          <div>
            <Label htmlFor="ward">Ward Number (Optional)</Label>
            <Input
              id="ward"
              type="text"
              value={form.ward}
              onChange={(e) => handleChange("ward", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="tole">Tole/Street Name (Optional)</Label>
            <Input
              id="tole"
              type="text"
              value={form.tole}
              onChange={(e) => handleChange("tole", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              type="text"
              value={form.address}
              onChange={(e) => handleChange("address", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="idType">ID Type</Label>
            <select
              id="idType"
              className="mt-1 w-full border rounded px-2 py-2"
              value={form.idType}
              onChange={(e) => handleChange("idType", e.target.value)}
            >
              <option value="">Select</option>
              <option value="Citizenship">Citizenship</option>
              <option value="NID">NID</option>
              <option value="Voter Card">Voter Card</option>
              <option value="Passport">Passport</option>
            </select>
          </div>

          <div>
            <Label htmlFor="idNumber">ID Number</Label>
            <Input
              id="idNumber"
              type="text"
              value={form.idNumber}
              onChange={(e) => handleChange("idNumber", e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="registerDate">Register Date</Label>
            <Input
              id="registerDate"
              type="date"
              value={form.registerDate}
              onChange={(e) => handleChange("registerDate", e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-col justify-end sm:flex-row gap-2 mt-4">
          <Button size="sm" type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" className="w-full sm:w-auto" size="sm">
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
}