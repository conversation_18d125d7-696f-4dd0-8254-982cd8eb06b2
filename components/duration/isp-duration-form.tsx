"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface DurationAddFormProps {
  onSubmit?: (durationData: any) => void;
  onCancel?: () => void;
  initialData?: any;
  isEdit?: boolean;
}

export default function durationAddForm({
  onSubmit,
  onCancel,
  initialData,
  isEdit = false,
}: DurationAddFormProps) {
  const [form, setForm] = useState({
    duration: initialData?.duration || "",
    description: initialData?.description || "",
  });

  // Form validation - check if all required fields are filled
  const isFormValid = () => {
    return form.duration.trim() !== "";
  };

  const handleChange = (field: keyof typeof form, value: string) => {
    setForm({ ...form, [field]: value });
  };

  const handleSubmit = () => {
    console.log("Submitted:", form);
    if (onSubmit) {
      onSubmit(form);
    }
    // Reset form after submission (only if not editing)
    if (!isEdit) {
      setForm({
        duration: "",
        description: "",
      });
    }
  };

  return (
    <div className="bg-white p-6 rounded shadow  mx-auto space-y-6">
      <h2 className="text-xl font-bold">
        {isEdit ? "Edit Duration" : "Add Duration"}
      </h2>

      {/* Form Fields */}
      <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Duration *</Label>
          <Input
            type="text"
            value={form.duration}
            onChange={(e) => handleChange("duration", e.target.value)}
            placeholder="e.g., 1 month, 3 months"
          />
        </div>
        <div>
          <Label>Description</Label>
          <Input
            type="text"
            value={form.description}
            onChange={(e) => handleChange("description", e.target.value)}
            placeholder="e.g., Monthly subscription"
          />
        </div>
      </div>

      <div className="flex gap-4 pt-4">
        <Button
          onClick={handleSubmit}
          className="bg-blue-600 hover:bg-blue-700"
          disabled={!isFormValid()}
        >
          {isEdit ? "Update Duration" : "Submit Duration"}
        </Button>
        {onCancel && (
          <Button onClick={onCancel} variant="outline">
            Cancel
          </Button>
        )}
      </div>
    </div>
  );
}
