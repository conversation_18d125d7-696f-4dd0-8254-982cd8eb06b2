"use client";

import { useState, useEffect } from "react";

import CustomerEdit from "@/components/customer/customer-edit";

import { CustomerType, usageData, UsageRecord } from "@/app/data/customers"; // `customers` import was unused, removed for cleaner code

import { <PERSON><PERSON> } from "@/components/ui/button";

// import RandomLineGraph from "./random-line-graph"; // Now imported by CustomerGraphSection

import { ListRestart, UserAdd, UserPen } from "@/components/icons/list";

// Import the new components

import CustomerBasicInfo from "@/components/customer/CustomerBasicInfo";

import CustomerServicePackageInfo from "@/components/customer/CustomerServicePackageInfo";

import CustomerConnectionAddressInfo from "@/components/customer/CustomerConnectionAddressInfo";

import CustomerRecentUsage from "@/components/customer/CustomerRecentUsage";

import CustomerHeader from "@/components/customer/CustomerHeader";

import CustomerRouterInfo from "@/components/customer/CustomerRouterInfo"; // New Router Info component

import CustomerUsageSection from "@/components/customer/CustomerUsageSection"; // New Usage component

import CustomerGraphSection from "@/components/customer/CustomerGraphSection"; // New Graph component

const TABS = [
  "Customer Details",

  "Router Info",

  // "Tickets",

  // "Subscription",

  "Usage",

  "Graph",
] as const;

type Props = {
  selectedCustomer: CustomerType;

  setSelectedCustomer: (c: CustomerType | null) => void;
};

export default function CustomerView({
  selectedCustomer,

  setSelectedCustomer,
}: Props) {
  const [showPassword, setShowPassword] = useState(false);

  const [showForm, setShowForm] = useState(false);

  const today = new Date();

  const year = today.getFullYear();

  const month = String(today.getMonth() + 1).padStart(2, "0");

  const day = String(today.getDate()).padStart(2, "0");

  const defaultStartDate = `${year}-${month}-01`;

  const defaultEndDate = `${year}-${month}-${day}`;

  const [activeTab, setActiveTab] = useState<(typeof TABS)[number]>(TABS[0]);

  const [startDate, setStartDate] = useState<string>(defaultStartDate);

  const [endDate, setEndDate] = useState<string>(defaultEndDate);

  const [appliedStartDate, setAppliedStartDate] =
    useState<string>(defaultStartDate);

  const [appliedEndDate, setAppliedEndDate] = useState<string>(defaultEndDate);

  const [selectedGraphRange, setSelectedGraphRange] = useState<string>("1day");

  const [showImagePopup, setShowImagePopup] = useState(false);

  const [currentImageSrc, setCurrentImageSrc] = useState<string | null>(null);

  useEffect(() => {
    setStartDate(defaultStartDate);

    setEndDate(defaultEndDate);

    setAppliedStartDate(defaultStartDate);

    setAppliedEndDate(defaultEndDate);
  }, [selectedCustomer.id, activeTab]);

  const customerUsageData = usageData.filter(
    (record) => record.customerId === selectedCustomer.id
  );

  const lastThreeUsageRecords = [...customerUsageData]

    .sort(
      (a, b) =>
        new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    )

    .slice(0, 3);

  const filteredUsageByDate = customerUsageData.filter((record) => {
    const recordStartTimestamp = new Date(record.startTime).getTime();

    const recordEndTimestamp = new Date(record.endTime).getTime();

    const filterStartTimestamp = appliedStartDate
      ? new Date(appliedStartDate + "T00:00:00").getTime()
      : null;

    const filterEndTimestamp = appliedEndDate
      ? new Date(appliedEndDate + "T23:59:59").getTime()
      : null;

    let passesFilter = true;

    if (
      filterStartTimestamp !== null &&
      recordEndTimestamp < filterStartTimestamp
    ) {
      passesFilter = false;
    }

    if (
      filterEndTimestamp !== null &&
      recordStartTimestamp > filterEndTimestamp
    ) {
      passesFilter = false;
    }

    return passesFilter;
  });

  const handleDateSubmit = () => {
    setAppliedStartDate(startDate);

    setAppliedEndDate(endDate);

    console.log("Applying usage data filter for:", startDate, "to", endDate);
  };

  const handleImageClick = (src: string) => {
    setCurrentImageSrc(src);

    setShowImagePopup(true);
  };

  const closeImagePopup = () => {
    setShowImagePopup(false);

    setCurrentImageSrc(null);
  };

  const handleEditCustomer = (customer: CustomerType) => {
    setSelectedCustomer(customer);

    setShowForm(true);
  };

  const graphRanges = [
    { label: "1 Day", value: "1day" },
    { label: "3 Days", value: "3days" },
    { label: "7 Days", value: "7days" },
    { label: "15 Days", value: "15days" },
    { label: "1 Month", value: "1month" },
    { label: "3 Months", value: "3months" },
    { label: "6 Months", value: "6months" },
    { label: "1 Year", value: "1year" },
  ];

  return (
    <div className="mb-1 space-y-4">
      {/* --- Header bar with name / status / address --- */}

      <CustomerHeader
        firstName={selectedCustomer.firstName}
        lastName={selectedCustomer.lastName}
        status={selectedCustomer.status}
        address={selectedCustomer.address}
      />

      {/* --- Tabs bar --- */}

      <div className="flex flex-wrap justify-between text-center bg-gray-50 border-b rounded-lg border-gray-300">
        {TABS.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex-1 min-w-[140px] py-2 text-sm ${
              activeTab === tab
                ? "border-b-2 border-blue-600 text-blue-600 font-medium"
                : "text-gray-700 hover:text-gray-900"
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* --- Content area based on activeTab --- */}

      <div>
        {activeTab === "Customer Details" && (
          <div className="grid grid-cols-1 gap-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <CustomerBasicInfo
                customer={selectedCustomer}
                showPassword={showPassword}
                onTogglePassword={() => setShowPassword(!showPassword)}
              />

              <CustomerServicePackageInfo customer={selectedCustomer} />

              <CustomerConnectionAddressInfo customer={selectedCustomer} />
            </div>

            <CustomerRecentUsage usageRecords={lastThreeUsageRecords} />
          </div>
        )}

        {activeTab === "Router Info" && (
          <CustomerRouterInfo
            customer={selectedCustomer}
            onImageClick={handleImageClick}
          />
        )}

        {activeTab === "Usage" && (
          <CustomerUsageSection
            startDate={startDate}
            setStartDate={setStartDate}
            endDate={endDate}
            setEndDate={setEndDate}
            handleDateSubmit={handleDateSubmit}
            filteredUsageByDate={filteredUsageByDate}
          />
        )}

        {activeTab === "Graph" && (
          <CustomerGraphSection
            selectedGraphRange={selectedGraphRange}
            setSelectedGraphRange={setSelectedGraphRange}
            graphRanges={graphRanges}
          />
        )}
      </div>

      {/* --- Back & Edit buttons (remain here as they affect overall page state) --- */}

      <div className="flex flex-col sm:flex-row gap-2 mt-4">
        <Button size="sm" onClick={() => setSelectedCustomer(null)}>
          <ListRestart /> Back to List{" "}
        </Button>

        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleEditCustomer(selectedCustomer)}
        >
          <UserPen /> Edit Customer
        </Button>
      </div>

      {/* --- Image Popup (Modal) --- */}

      {showImagePopup && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex justify-center items-center z-50 p-4"
          onClick={closeImagePopup}
        >
          <div className="relative" onClick={(e) => e.stopPropagation()}>
            <img
              src={currentImageSrc || ""}
              alt="Full view"
              className="max-h-[90vh] max-w-[90vw] object-contain rounded-lg shadow-xl"
            />

            <button
              className="absolute top-2 right-2 text-white text-2xl font-bold bg-gray-800 rounded-full w-8 h-8 flex items-center justify-center cursor-pointer hover:bg-gray-700 transition-colors duration-200"
              onClick={closeImagePopup}
              aria-label="Close"
            >
              &times;
            </button>
          </div>
        </div>
      )}

      {/* CustomerEdit Form Modal (remains here as it interacts with selectedCustomer) */}

      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40 p-4">
          <div className="bg-white rounded-lg p-6 max-h-[90vh] overflow-y-auto w-full max-w-2xl relative">
            <button
              onClick={() => setShowForm(false)}
              className="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl"
              aria-label="Close" // Add aria-label for accessibility
            >
              &times; {/* <--- ADD THIS CONTENT */}
            </button>

            <CustomerEdit
              customer={selectedCustomer}
              onCancel={() => setShowForm(false)}
              onSave={() => {
                // You would typically refetch the customer data here

                // or update the `selectedCustomer` state from the response of CustomerEdit

                setShowForm(false);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
