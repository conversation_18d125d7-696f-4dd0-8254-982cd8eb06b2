"use client";

import { useState, useEffect, React } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { User, UserGroup, UserOrganization } from "./user-management";
import apiClient from "@/lib/apiClient";
import { Group } from "./add-user";
import { Organization } from "./add-user";

export interface EditUserFormProps {
  user: User;
  onSubmit: (user: User) => void;
}

export function EditUserForm({ user, onSubmit }: EditUserFormProps) {
  const [editedUser, setEditedUser] = useState(user);
  const [editedEmail, setEditedEmail] = useState(user.email);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [groups, setGroups] = useState<Group[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loadingGroups, setLoadingGroups] = useState(true);
  const [loadingOrganizations, setLoadingOrganizations] = useState(true);

  // Store selected IDs
  const [selectedGroupId, setSelectedGroupId] = useState(user.group_id || "");
  const [selectedOrgId, setSelectedOrgId] = useState(user.org_id || "");
  const [selectedStatus, setSelectedStatus] = useState(user.status || "");

  useEffect(() => {
    setEditedUser(user);
    setEditedEmail(user.email);
    setSelectedGroupId(user.group_id || "");
    setSelectedOrgId(user.org_id);
    setSelectedStatus(user.status);
  }, [user]);

  // Fetch groups from backend
  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await apiClient.get("/groups");
        setGroups(response.data.data);
      } catch (error) {
        console.error("Failed to fetch groups:", error);
      } finally {
        setLoadingGroups(false);
      }
    };

    fetchGroups();
  }, []);

  // Fetch organizations from backend
  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const response = await apiClient.get("/organization");
        setOrganizations(response.data.data);
      } catch (error) {
        console.error("Failed to fetch organizations:", error);
      } finally {
        setLoadingOrganizations(false);
      }
    };
    fetchOrganizations();
  }, []);

  const isValid =
    editedUser.username.trim() !== "" &&
    (newPassword === "" || newPassword === confirmPassword);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      username: editedUser.username,
      password: newPassword !== "" ? newPassword : user.password, 
      email: editedEmail,
      status: selectedStatus,
      org_id: selectedOrgId,
      group_id: selectedGroupId,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Username</label>
        <Input
          value={editedUser.username}
          onChange={(e) =>
            setEditedUser((prev) => ({ ...prev, username: e.target.value }))
          }
          placeholder="Username"
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">
          New Password
        </label>
        <div className="relative">
          <Input
            type={showPassword ? "text" : "password"}
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="New Password (leave blank to keep current)"
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 -translate-y-1 h-8"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? "Hide" : "Show"}
          </Button>
        </div>
      </div>

      {newPassword && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Confirm New Password
          </label>
          <div className="relative">
            <Input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm New Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 -translate-y-1 h-8"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? "Hide" : "Show"}
            </Button>
          </div>
        </div>
      )}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Email</label>
        <Input
          value={editedEmail}
          onChange={(e) => setEditedEmail(e.target.value)}
          placeholder="Email Address"
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">
          Organization
        </label>
        <OrganizationSelect
          organizations={organizations}
          loading={loadingOrganizations}
          value={selectedOrgId}
          onChange={setSelectedOrgId}
        />
      </div>

      <GroupSelect
        value={selectedGroupId}
        onChange={setSelectedGroupId}
        groups={groups}
        loading={loadingGroups}
      />

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Status</label>
        <select
          className="w-full p-2 border rounded-md"
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          required
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      <Button type="submit" className="w-full" disabled={!isValid}>
        Save Changes
      </Button>
    </form>
  );
}

function OrganizationSelect({
  value,
  onChange,
  organizations,
  loading,
}: {
  value: string;
  onChange: (organizationId: string) => void;
  organizations: Organization[];
  loading: boolean;
}) {
  return (
    <select
      className="w-full p-2 border rounded-md"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    >
      <option value="">Select an organization</option>
      {organizations.map((org) => (
        <option key={org.id} value={org.id}>
          {org.name}
        </option>
      ))}
    </select>
  );
}

function GroupSelect({
  value,
  onChange,
  groups,
  loading,
}: {
  value: string;
  onChange: (groupId: string) => void;
  groups: Group[];
  loading: boolean;
}) {
  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-gray-700">Group</label>
      <select
        className="w-full p-2 border rounded-md"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        required
      >
        <option value="">Select a group</option>
        {groups.map((group) => (
          <option key={group.id} value={group.id}>
            {group.name}
          </option>
        ))}
      </select>
    </div>
  );
}
