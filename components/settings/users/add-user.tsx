"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import apiClient from "@/lib/apiClient";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";

export type UserGroup = string;
export type UserOrganization = string;

export interface Group {
  id: number;
  name: string;
}

export interface Organization {
  id: number;
  name: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  group: UserGroup;
  password: string;
  organization: UserOrganization;
  status: string;
}

export function AddUserForm({ }: { onSubmit: (user: User) => void }) {
  const router = useRouter();
  const [newUser, setNewUser] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    email: "",
    organization: "" as UserOrganization,
    group: "" as UserGroup,
    status: "active",
  }); // const access_token = Cookies.get("accessToken");

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [groups, setGroups] = useState<Group[]>([]);
  const [loadingGroups, setLoadingGroups] = useState(true);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loadingOrganizations, setLoadingOrganizations] = useState(true);
  const [selectedGroupId, setSelectedGroupId] = useState("");
  const [selectedOrganizationId, setSelectedOrganizationId] = useState("");

  // Fetch from backend
  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await apiClient.get("/groups");
        setGroups(response.data.data);
        // console.log(response.data.data);
      } catch (error) {
        console.error("Failed to fetch groups:", error);
      } finally {
        setLoadingGroups(false);
      }
    };

    fetchGroups();
  }, []);

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const response = await apiClient.get("/organization");
        setOrganizations(response.data.data);
      } catch (error) {
        console.error("Failed to fetch organizations:", error);
      } finally {
        setLoadingOrganizations(false);
      }
    };

    fetchOrganizations();
  }, []);

  const handlePasswordChange = (password: string) => {
    setNewUser((prev) => ({ ...prev, password }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await apiClient.post("/user", {
        username: newUser.username.trim(),
        password: newUser.password.trim(),
        email: newUser.email.trim(),
        status: newUser.status.toLowerCase(),
        org_id: selectedOrganizationId.trim(),
        group_id: selectedGroupId.trim(),
      });
      console.log("User created:", response.data);
      router.push("/app/settings/users");
    } catch (error) {
      console.error("Failed to add user:", error);
      alert("Failed to add user");
    }
  };

  const isValid =
    newUser.username.trim() !== "" &&
    newUser.email.trim() !== "" &&
    newUser.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) &&
    selectedOrganizationId.trim() !== "" &&
    selectedGroupId.trim() !== "" &&
    newUser.password === newUser.confirmPassword;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="text-sm font-medium text-gray-700">Username</label>
        <Input
          placeholder="Username"
          value={newUser.username}
          onChange={(e) =>
            setNewUser((prev) => ({ ...prev, username: e.target.value }))
          }
        />
      </div>

      <div className="relative">
        <label className="text-sm font-medium text-gray-700"> Password </label>
        <Input
          type={showPassword ? "text" : "password"}
          placeholder="Password"
          value={newUser.password}
          onChange={(e) => handlePasswordChange(e.target.value)}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 -translate-y-1 h-8"
          onClick={() => setShowPassword(!showPassword)}
        >
          {showPassword ? "Hide" : "Show"}
        </Button>
      </div>

      <div className="relative">
        <label className="text-sm font-medium text-gray-700">
          {" "}
          Confirm Password
        </label>
        <Input
          type={showConfirmPassword ? "text" : "password"}
          placeholder="Confirm Password"
          value={newUser.confirmPassword}
          onChange={(e) =>
            setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))
          }
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 -translate-y-1 h-8"
          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
        >
          {showConfirmPassword ? "Hide" : "Show"}
        </Button>
      </div>
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">
          Email Address
        </label>
        <Input
          type="email"
          placeholder="Email Address"
          value={newUser.email}
          pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
          required
          onChange={(e) =>
            setNewUser((prev) => ({ ...prev, email: e.target.value }))
          }
        />
        {newUser.email &&
          !newUser.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) && (
            <span className="text-sm text-red-500">
              Please enter a valid email
            </span>
          )}
      </div>
      <OrganizationSelect
        selectedOrganizationId={selectedOrganizationId}
        setSelectedOrganizationId={setSelectedOrganizationId}
        organizations={organizations}
        loading={loadingOrganizations}
      />

      <GroupSelect
        // value={newUser.group}
        selectedGroupId={selectedGroupId}
        setSelectedGroupId={setSelectedGroupId}
        groups={groups}
        loading={loadingGroups}
      />

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Status</label>
        <select
          className="w-full p-2 border rounded-md"
          value={newUser.status}
          onChange={(e) =>
            setNewUser((prev) => ({ ...prev, status: e.target.value }))
          }
          required
        >
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      <Button type="submit" className="w-full" disabled={!isValid}>
        Create User
      </Button>
    </form>
  );
}

function GroupSelect({
  // value,
  selectedGroupId,
  setSelectedGroupId,
  groups,
  loading,
}: {
  // value: UserGroup;
  selectedGroupId: string;
  setSelectedGroupId: (group: UserGroup) => void;
  groups: Group[];
  loading: boolean;
}) {

  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-gray-700">Group</label>
      <select
        className="w-full p-2 border rounded-md"
        value={selectedGroupId}
        onChange={(e) => setSelectedGroupId(e.target.value)}
        required
      >
        <option value="">Select a group</option>
        {groups.map((group) => (
          <option key={group.id} value={group.id.toString()}>
            {group.name}
          </option>
        ))}
      </select>
    </div>
  );
}

function OrganizationSelect({
  selectedOrganizationId,
  setSelectedOrganizationId,
  organizations,
  loading,
}: {
  selectedOrganizationId: string;
  setSelectedOrganizationId: (organization: UserOrganization) => void;
  organizations: Organization[];
  loading: boolean;
}) {

  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-gray-700">Organization</label>
      <select
        className="w-full p-2 border rounded-md"
        value={selectedOrganizationId}
        onChange={(e) => setSelectedOrganizationId(e.target.value)}
        required
      >
        <option value="">Select an organization</option>
        {organizations.map((organization) => (
          <option key={organization.id} value={organization.id.toString()}>
            {organization.name}
          </option>
        ))}
      </select>
    </div>
  );
}
