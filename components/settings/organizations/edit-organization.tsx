"use client";

import { React, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { organization } from "./organization-management";

export interface EditOrganizationFormProps {
  organization: organization;
  onSubmit: (organization: organization) => void;
}

export function EditOrganizationForm({ organization, onSubmit }: EditOrganizationFormProps) {
  const [editedOrganization, setEditedOrganization] = useState("");

  const isValid = editedOrganization.trim() !== "";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid) {
      onSubmit({
        ...organization,
        name: editedOrganization.trim()
      });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Organization Name</label>
        <Input
          value={editedOrganization}
          onChange={(e) => setEditedOrganization(e.target.value)}
          placeholder="Enter the Organization Name"
        />
      </div>
      <Button type="submit" className="w-full" disabled={!isValid}>
        Save Changes
      </Button>
    </form>
  );
}
