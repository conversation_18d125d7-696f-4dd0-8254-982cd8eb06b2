"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { NAS, NASFormData } from "@/types/nas";

interface NASFormProps {
  initialData?: NAS;
  onSubmit: (data: NASFormData) => void;
  onCancel?: () => void;
  isEdit?: boolean;
}

export default function NASForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isEdit = false 
}: NASFormProps) {
  const [formData, setFormData] = useState<NASFormData>({
    nasname: initialData?.nasname || "",
    secret: initialData?.secret || "",
    description: initialData?.description || "",
    vendor: initialData?.vendor || "",
  });

  const [errors, setErrors] = useState<Partial<NASFormData>>({});

  const handleChange = (field: keyof NASFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<NASFormData> = {};

    if (!formData.nasname.trim()) {
      newErrors.nasname = "NAS Name is required";
    }

    if (!formData.secret.trim()) {
      newErrors.secret = "Secret is required";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }

    if (!formData.vendor.trim()) {
      newErrors.vendor = "Vendor is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* NAS Name */}
        <div className="space-y-2">
          <Label htmlFor="nasname">NAS Name *</Label>
          <Input
            id="nasname"
            type="text"
            value={formData.nasname}
            onChange={(e) => handleChange("nasname", e.target.value)}
            placeholder="Enter NAS name (e.g., ***********)"
            className={errors.nasname ? "border-red-500" : ""}
          />
          {errors.nasname && (
            <p className="text-sm text-red-500">{errors.nasname}</p>
          )}
        </div>

        {/* Vendor */}
        <div className="space-y-2">
          <Label htmlFor="vendor">Vendor *</Label>
          <select
            id="vendor"
            value={formData.vendor}
            onChange={(e) => handleChange("vendor", e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.vendor ? "border-red-500" : "border-gray-300"
            }`}
          >
            <option value="">Select Vendor</option>
            <option value="mikrotik">Mikrotik</option>
            <option value="cisco">Cisco</option>
            <option value="ubiquiti">Ubiquiti</option>
            <option value="huawei">Huawei</option>
            <option value="other">Other</option>
          </select>
          {errors.vendor && (
            <p className="text-sm text-red-500">{errors.vendor}</p>
          )}
        </div>
      </div>

      {/* Secret */}
      <div className="space-y-2">
        <Label htmlFor="secret">Secret *</Label>
        <Input
          id="secret"
          type="password"
          value={formData.secret}
          onChange={(e) => handleChange("secret", e.target.value)}
          placeholder="Enter secret key"
          className={errors.secret ? "border-red-500" : ""}
        />
        {errors.secret && (
          <p className="text-sm text-red-500">{errors.secret}</p>
        )}
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleChange("description", e.target.value)}
          placeholder="Enter description"
          rows={4}
          className={errors.description ? "border-red-500" : ""}
        />
        {errors.description && (
          <p className="text-sm text-red-500">{errors.description}</p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex gap-4 pt-4">
        <Button type="submit" className="flex-1">
          {isEdit ? "Update NAS" : "Add NAS"}
        </Button>
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
}
