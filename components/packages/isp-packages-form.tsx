"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface PackagesAddFormProps {
  onSubmit?: (packageData: any) => void;
  onCancel?: () => void;
  initialData?: any;
  isEdit?: boolean;
}

export default function packagesAddForm({
  onSubmit,
  onCancel,
  initialData,
  isEdit = false,
}: PackagesAddFormProps) {
  const [form, setForm] = useState({
    package_name: initialData?.package_name || "",
    upload: initialData?.upload || "",
    download: initialData?.download || "",
    price: initialData?.price || "",
    status: initialData?.status || "Active",
    fupdetails: initialData?.fupdetails || "",
    fupduration: initialData?.fupduration || "",
    // registerDate: initialData?.registerDate || "",
    fupLimit: initialData?.fupLimit || "",
    postFUPSpeed: initialData?.postFUPSpeed || "",
  });

  // Form validation - check if all required fields are filled
  const isFormValid = () => {
    return (
      form.package_name.trim() !== "" &&
      form.upload.trim() !== "" &&
      form.download.trim() !== "" &&
      form.price.trim() !== ""
    );
  };

  const handleChange = (field: keyof typeof form, value: string) => {
    setForm({ ...form, [field]: value });
  };

  const handleSubmit = () => {
    console.log("Submitted:", form);
    if (onSubmit) {
      onSubmit(form);
    }
    // Reset form after submission
    setForm({
      package_name: "",
      upload: "",
      download: "",
      price: "",
      status: "Active",
      fupdetails: "",
      fupduration: "",
      // registerDate: "",
      fupLimit: "",
      postFUPSpeed: "",
    });
  };

  return (
    <div className="bg-white p-6 rounded shadow  mx-auto space-y-6">
      <h2 className="text-xl font-bold">
        {isEdit ? "Edit Package" : "Add Package"}
      </h2>

      {/* Dropdown Fields */}
      <div className="text-xs grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Package Name *</Label>
          <Input
            type="text"
            value={form.package_name}
            onChange={(e) => handleChange("package_name", e.target.value)}
            placeholder="e.g., 100/100 Mbps"
          />
        </div>

        <div>
          <div className="space-y-2">
            <Label>Upload Speed *</Label>
            <Input
              type="text"
              value={form.upload}
              onChange={(e) => handleChange("upload", e.target.value)}
              placeholder="e.g., 100M"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label>Download Speed *</Label>
          <Input
            type="text"
            value={form.download}
            onChange={(e) => handleChange("download", e.target.value)}
            placeholder="e.g., 100M"
          />
        </div>

        <div className="space-y-2">
          <Label>Price *</Label>
          <Input
            type="text"
            value={form.price}
            onChange={(e) => handleChange("price", e.target.value)}
            placeholder="e.g., 2,500"
          />
        </div>

        {/* <div className="space-y-2">
          <Label>Status *</Label>
          <div className="flex gap-4">
            <button
              type="button"
              className={`w-16 h-6 border-2 rounded-lg flex items-center justify-center
                ${
                  form.status === "Active"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black-700"
                }
                hover:border-blue-400 transition-colors font-medium`}
              onClick={() =>
                handleChange(
                  "status",
                  form.status?.trim() ? form.status : "Active"
                )
              }
            >
              Active
            </button>

            <button
              type="button"
              className={`w-16 h-6 border-2 rounded-lg flex items-center justify-center
                ${
                  form.status === "Inactive"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black-700"
                }
                hover:border-blue-400 transition-colors font-medium`}
              onClick={() =>
                handleChange(
                  "status",
                  form.status?.trim() ? form.status : "Inactive"
                  // form.status === "Inactive" ? "" : "Inactive"
                )
              }
            >
              Inactive
            </button>
          </div>
        </div> */}

        <div className="space-y-2">
          <Label>Status *</Label>
          <div className="flex gap-4">
            {/* Active Button */}
            <button
              type="button"
              aria-pressed={form.status === "Active"}
              className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
        ${
          form.status === "Active"
            ? "border-blue-600 bg-blue-600 text-white"
            : "border-gray-300 bg-white text-black"
        }
        hover:border-blue-400 transition-colors font-medium`}
              onClick={() => handleChange("status", "Active")}
            >
              Active
            </button>

            {/* Inactive Button */}
            <button
              type="button"
              aria-pressed={form.status === "Inactive"}
              className={`w-20 h-8 border-2 rounded-lg flex items-center justify-center
        ${
          form.status === "Inactive"
            ? "border-blue-600 bg-blue-600 text-white"
            : "border-gray-300 bg-white text-black"
        }
        hover:border-blue-400 transition-colors font-medium`}
              onClick={() => handleChange("status", "Inactive")}
            >
              Inactive
            </button>
          </div>
        </div>

        {/* <div className="space-y-2">
          <Label>Package Registered Date</Label>
          <Input
            type="date"
            value={form.registerDate}
            onChange={(e) => handleChange("registerDate", e.target.value)}
          />
        </div> */}

        <div className="space-y-4">
          <Label>Apply FUP to this package?</Label>
          <div className="flex gap-4">
            {/* YES Button */}
            <button
              type="button"
              className={`w-10 h-6 border-2 rounded-lg flex items-center justify-center
                ${
                  form.fupdetails === "yes"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black-700"
                }
                hover:border-blue-400 transition-colors font-medium`}
              onClick={() =>
                handleChange(
                  "fupdetails",
                  form.fupdetails === "yes" ? "" : "yes"
                )
              }
            >
              YES
            </button>

            {/* NO Button */}
            <button
              type="button"
              className={`w-10 h-6 border-2 rounded-lg flex items-center justify-center
                ${
                  form.fupdetails === "no"
                    ? "border-blue-600 bg-blue-600 text-white"
                    : "border-gray-300 bg-white text-black-700"
                }
                hover:border-blue-400 transition-colors font-medium`}
              onClick={() =>
                handleChange("fupdetails", form.fupdetails === "no" ? "" : "no")
              }
            >
              NO
            </button>
          </div>

          {/* Show additional options only when YES is selected */}
          {form.fupdetails === "yes" && (
            <div className="mt-4 space-y-4">
              <div>
                <Label>FUP Limit (GB)</Label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  value={form.fupLimit}
                  onChange={(e) => handleChange("fupLimit", e.target.value)}
                />
              </div>
              <div>
                <Label>Post-FUP Speed (Mbps)</Label>
                <input
                  type="number"
                  className="mt-1 w-full border rounded px-3 py-2"
                  value={form.postFUPSpeed}
                  onChange={(e) => handleChange("postFUPSpeed", e.target.value)}
                />
              </div>
              <div>
                <Label>Apply FUP on</Label>
                <select
                  className="mt-1 w-full border rounded px-2 py-2"
                  value={form.fupduration}
                  onChange={(e) => handleChange("fupduration", e.target.value)}
                >
                  <option value="">Select</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="fortnightly">Fortnightly</option>
                  <option value="Monthly">Monthly</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-4 pt-4">
        <Button
          onClick={handleSubmit}
          className="bg-blue-600 hover:bg-blue-700"
          disabled={!isFormValid()}
        >
          {isEdit ? "Update Package" : "Submit Package"}
        </Button>
        {onCancel && (
          <Button onClick={onCancel} variant="outline">
            Cancel
          </Button>
        )}
      </div>
    </div>
  );
}
