"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import  ISPpackagesForm from "@/components/packages/isp-packages-form"

export default function PackageDetails() {
  const [showForm, setShowForm] = useState(false)

  return (
    <div className="p-5  mx-auto bg-white rounded-lg">
      <div className="flex justify-between items-center  mb-6">
        <Button size="sm" onClick={() => setShowForm(!showForm)}>
          {showForm ? "Cancel" : "Add Package"}
        </Button>
      </div>

      {showForm ? (
        <div className="mb-6">
          <ISPpackagesForm />
        </div>
      ) : null}
    </div>
  )
}