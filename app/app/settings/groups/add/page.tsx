"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  AddGroupForm,
  group,
} from "@/components/settings/groups/group-management";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";

export default function AddGroupPage() {
  const router = useRouter();

  const handleSubmit = async (groupname: group) => {
    try {
      const response = await apiClient.post("/group", groupname);
      console.log("Group created:", response.data);
      router.push("/app/settings/groups");
    } catch (error) {
      console.error("Failed to add group:", error);
      alert("Failed to add group");
    }
  };

  const handleCancel = () => {
    router.push("/app/settings/groups");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Groups
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Add New Group</h1>
        <p className="text-gray-600 mt-2">
          Create a new group with the required information.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <AddGroupForm onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
