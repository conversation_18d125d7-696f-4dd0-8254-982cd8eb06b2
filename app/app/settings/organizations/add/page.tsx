"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  AddOrganizationForm,
  organization,
} from "@/components/settings/organizations/organization-management";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";

export default function AddOrganizationPage() {
  const router = useRouter();

  const handleSubmit = async (newOrganization: organization) => {
    try{
      const response = await apiClient.post("/organization", newOrganization);
      console.log("Organization created:", response.data);
    }
    catch (error) {
      console.error("Failed to add organization:", error);
      alert("Failed to add organization");
    }

    router.push("/app/settings/organizations");
  };

  const handleCancel = () => {
    router.push("/app/settings/organizations");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Organizations
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Add New Organization</h1>
        <p className="text-gray-600 mt-2">
          Create a new organization with the required information.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <AddOrganizationForm onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
