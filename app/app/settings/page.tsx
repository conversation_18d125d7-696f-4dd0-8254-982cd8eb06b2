// "use client";

// import { useState, useEffect } from "react";

export default function SettingsPage() {
  // const [users, setUsers] = useState<User[]>([]);
  // const [loading, setLoading] = useState(false);

  return (
    <div className="p-6">
      <div className="grid grid-cols-5 gap-4 mb-6">
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">35</div>
            <div className="text-sm text-gray-500">Total Users</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">3 / 35</div>
            <div className="text-sm text-gray-500">Super Admin</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">5 / 35</div>
            <div className="text-sm text-gray-500">Admin</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">20 / 35</div>
            <div className="text-sm text-gray-500">Support</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">17 / 35</div>
            <div className="text-sm text-gray-500">Sales</div>
          </div>
        </div>
      </div>
    </div>
  );
}
