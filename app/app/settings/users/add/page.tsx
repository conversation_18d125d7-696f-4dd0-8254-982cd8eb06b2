"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { AddUserForm, User } from "@/components/settings/users/add-user";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";

export default function AddUserPage() {
  const router = useRouter();

  const handleSubmit = async (newUser: User) => {
    try{
      const response = await apiClient.post("/user", newUser);
      console.log("User created:", response.data);
    }
    catch (error) {
      console.error("Failed to add user:", error);
      alert("Failed to add user");
    }

    router.push("/app/settings/users");
  };

  const handleCancel = () => {
    router.push("/app/settings/users");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Users
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Add New User</h1>
        <p className="text-gray-600 mt-2">
          Create a new user account with required group.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <AddUserForm onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
