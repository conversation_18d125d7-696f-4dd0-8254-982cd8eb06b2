"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { UserTable, User } from "@/components/settings/users/user-management";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import apiClient from "@/lib/apiClient";
import { Trash2 } from "@/components/icons/list";

export default function UserManagementPage() {
  const router = useRouter();
  const [userData, setUser] = useState<User[]>([]);
  const [search, setSearch] = useState("");

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await apiClient.get("/user");
        setUser(response.data);
      } catch (error) {
        console.error("Failed to fetch users:", error);
      }
    };

    fetchUsers();
  }, []);

  const handleDeleteUser = async (id: string) => {
    try {
      const response = await apiClient.delete(`/user/${id}`);
      console.log("User deleted:", response.data);
      setUser(userData.filter((user) => user.id !== id));
    } catch (error) {
      console.error("Failed to delete user:", error);
    }
  };

  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center flex-wrap gap-4">
        <h1 className="text-2xl font-bold">User Management Portal</h1>
        <div className="flex items-center gap-3">
          <Input
            placeholder="Search users..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-60"
          />
          <Button onClick={() => router.push("/app/settings/users/add")}>
            Add New User
          </Button>
        </div>
      </div>

      <UserTable
        users={userData.filter((user) =>
          user.username.toLowerCase().includes(search.toLowerCase())
        )}
        onDelete={handleDeleteUser}
      />
    </div>
  );
}

export function DeleteUserDialog({
  username,
  onDelete,
}: {
  username: string;
  onDelete: () => void;
}) {
  const [showConfirm, setShowConfirm] = useState(false);
  const [inputName, setInputName] = useState("");

  const handleDelete = () => {
    if (inputName === username) {
      onDelete();
      setShowConfirm(false);
    } else {
      alert("Username does not match");
    }
  };

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        setShowConfirm(false);
        setInputName("");
      }
    };

    if (showConfirm) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showConfirm]);

  return (
    <>
      <button
        onClick={() => setShowConfirm(true)}
        className="bg-red-600 text-white px-1 py-0 space-x-5 rounded"
      >
        <Trash2 />
      </button>

      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded">
          <div className="bg-white p-7 rounded w-auto">
            <h2 className="text-xl font-bold mb-4 text-center">
              Confirm Deletion
            </h2>
            <p className="mb-2 text-left">
              Are you sure you want to delete the user{" "}
              <strong>{username}</strong>?
            </p>
            <p className="mb-4 text-sm text-gray-600 text-left">
              Type the username to confirm:
            </p>
            <input
              className="border px-3 py-2 w-full mb-4"
              value={inputName}
              onChange={(e) => setInputName(e.target.value)}
              placeholder="Enter username"
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowConfirm(false)}
                className="px-4 py-2 bg-gray-300 rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded"
              >
                Confirm Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
