"use client";

import { useRouter, usePara<PERSON> } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { EditUserForm } from "@/components/settings/users/edit-user";
import { User } from "@/components/settings/users/user-management";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);

  // Get user data from sessionStorage on mount
  useEffect(() => {
    try {
      const userData = sessionStorage.getItem("editUser");
      if (userData) {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
      }
    } catch (error) {
      console.error("Failed to parse user data from sessionStorage:", error);
    }
  }, []);

  const handleSubmit = async (updatedUser: User) => {
    try {
      const response = await apiClient.patch(`/user/${userId}`, updatedUser);
      console.log("User updated:", response.data);
      sessionStorage.removeItem("editUser");
      router.push("/app/settings/users");
    } catch (error) {
      console.error("Failed to update user:", error);
      alert("Failed to update user");
    }
  };

  const handleCancel = () => {
    sessionStorage.removeItem("editUser");
    router.push("/app/settings/users");
  };

  if (loading) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="text-center py-8">
          <p>Loading user data...</p>
        </div>
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Users
          </Button>
        </div>
        <div className="text-center py-8">
          <p className="text-red-600">
            No user data found. Please go back and try again.
          </p>
          <Button onClick={handleCancel} className="mt-4">
            Go Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Users
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Edit User</h1>
        <p className="text-gray-600 mt-2">
          Update user information for{" "}
          <strong>{user?.username || "Unknown User"}</strong>.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        {user && <EditUserForm user={user} onSubmit={handleSubmit} />}

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
