"use client";

import { useRouter, usePara<PERSON> } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import ISPpackagesForm from "@/components/packages/isp-packages-form";
import { ArrowLeft } from "lucide-react";

// Sample data - in a real app, this would come from an API
const packages = [
  {
    id: 1,
    type: "package",
    packagename: "100/100 Mbps",
    up_speed: "100Mbps",
    down_speed: "100Mbps",
    description: "100Mbps package with 50Gbps FUP on a weekly basis",
    fupdetails: "50Gbps",
    price: "NPR 2,500",
  },
  {
    id: 2,
    type: "package",
    packagename: "200/200 Mbps",
    up_speed: "200Mbps",
    down_speed: "200Mbps",
    description: "200Mbps package with 100Gbps FUP on a weekly basis",
    fupdetails: "100Gbps",
    price: "NPR 4,000",
  },
  {
    id: 3,
    type: "package",
    packagename: "300/300 Mbps",
    up_speed: "300Mbps",
    down_speed: "300Mbps",
    description: "300Mbps package with 50Gbps FUP on a daily basis",
    fupdetails: "50Gbps",
    price: "NPR 6,000",
  },
];

export default function EditPackagePage() {
  const router = useRouter();
  const params = useParams();
  const packageId = params.id as string;
  
  const [packageData, setPackageData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real application, you would fetch the package data from an API
    // For now, we'll simulate this with mock data
    const fetchPackage = () => {
      const id = parseInt(packageId);
      const foundPackage = packages.find(pkg => pkg.id === id);
      
      if (foundPackage) {
        setPackageData(foundPackage);
      }
      setLoading(false);
    };

    if (packageId) {
      fetchPackage();
    }
  }, [packageId]);

  const handleSubmit = (updatedPackage: any) => {
    // Here you would typically make an API call to update the package
    // For now, we'll just log the updated package and redirect back
    console.log("Package updated:", updatedPackage);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch(`/api/packages/${packageId}`, {
    //     method: 'PUT',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(updatedPackage)
    //   });
    //   // Show success message
    //   router.push("/app/packages");
    // } catch (error) {
    //   // Handle error
    // }
    
    // For now, just redirect back to packages page
    router.push("/app/packages");
  };

  const handleCancel = () => {
    router.push("/app/packages");
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Packages
          </Button>
          <h1 className="text-2xl font-bold">Edit Package</h1>
        </div>
        <div className="text-center py-8">Loading...</div>
      </div>
    );
  }

  if (!packageData) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Packages
          </Button>
          <h1 className="text-2xl font-bold">Edit Package</h1>
        </div>
        <div className="text-center py-8 text-red-500">Package not found</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Packages
        </Button>
        <h1 className="text-2xl font-bold">Edit Package: {packageData.packagename}</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <ISPpackagesForm 
          onSubmit={handleSubmit} 
          onCancel={handleCancel}
          initialData={packageData}
          isEdit={true}
        />
      </div>
    </div>
  );
}
