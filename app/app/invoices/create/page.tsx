"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Plus, Trash2 } from "lucide-react";
import { generateInvoiceNumber, calculateInvoiceTotals, InvoiceItem } from "@/app/data/invoices";

export default function CreateInvoicePage() {
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    customerName: "",
    customerEmail: "",
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: "",
    notes: "",
    taxRate: 13,
  });

  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: 1,
      description: "",
      quantity: 1,
      rate: 0,
      amount: 0,
    },
  ]);

  const handleFormChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleItemChange = (id: number, field: keyof InvoiceItem, value: string | number) => {
    setItems(prev => prev.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        if (field === 'quantity' || field === 'rate') {
          updatedItem.amount = Number(updatedItem.quantity) * Number(updatedItem.rate);
        }
        return updatedItem;
      }
      return item;
    }));
  };

  const addItem = () => {
    const newId = Math.max(...items.map(item => item.id)) + 1;
    setItems(prev => [...prev, {
      id: newId,
      description: "",
      quantity: 1,
      rate: 0,
      amount: 0,
    }]);
  };

  const removeItem = (id: number) => {
    if (items.length > 1) {
      setItems(prev => prev.filter(item => item.id !== id));
    }
  };

  const { subtotal, tax, total } = calculateInvoiceTotals(items, formData.taxRate);

  const isFormValid = () => {
    return (
      formData.customerName.trim() !== "" &&
      formData.customerEmail.trim() !== "" &&
      formData.dueDate !== "" &&
      items.some(item => item.description.trim() !== "" && item.amount > 0)
    );
  };

  const handleSubmit = () => {
    if (!isFormValid()) {
      alert("Please fill in all required fields");
      return;
    }

    const newInvoice = {
      id: Date.now(), // In real app, this would be generated by backend
      invoiceNumber: generateInvoiceNumber(),
      customerId: Date.now(), // In real app, this would be selected from customers
      customerName: formData.customerName,
      customerEmail: formData.customerEmail,
      issueDate: formData.issueDate,
      dueDate: formData.dueDate,
      status: "Draft" as const,
      items: items.filter(item => item.description.trim() !== ""),
      subtotal,
      tax,
      taxRate: formData.taxRate,
      total,
      notes: formData.notes,
    };

    console.log("Creating invoice:", newInvoice);
    // In a real app, you would make an API call here
    
    alert("Invoice created successfully!");
    router.push("/app/invoices");
  };

  const handleCancel = () => {
    router.push("/app/invoices");
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Invoices
        </Button>
        <h1 className="text-2xl font-bold">Create New Invoice</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Customer Information</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Customer Name *
              </label>
              <input
                type="text"
                value={formData.customerName}
                onChange={(e) => handleFormChange("customerName", e.target.value)}
                className="w-full p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
                placeholder="Enter customer name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Customer Email *
              </label>
              <input
                type="email"
                value={formData.customerEmail}
                onChange={(e) => handleFormChange("customerEmail", e.target.value)}
                className="w-full p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
                placeholder="Enter customer email"
              />
            </div>
          </div>

          {/* Invoice Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Invoice Details</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Issue Date *
              </label>
              <input
                type="date"
                value={formData.issueDate}
                onChange={(e) => handleFormChange("issueDate", e.target.value)}
                className="w-full p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Due Date *
              </label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => handleFormChange("dueDate", e.target.value)}
                className="w-full p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tax Rate (%)
              </label>
              <input
                type="number"
                value={formData.taxRate}
                onChange={(e) => handleFormChange("taxRate", Number(e.target.value))}
                className="w-full p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
                min="0"
                max="100"
                step="0.1"
              />
            </div>
          </div>
        </div>

        {/* Invoice Items */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Invoice Items</h3>
            <Button onClick={addItem} variant="outline" size="sm" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Item
            </Button>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full border">
              <thead className="bg-gray-50">
                <tr>
                  <th className="border px-4 py-2 text-left">Description *</th>
                  <th className="border px-4 py-2 text-left">Quantity</th>
                  <th className="border px-4 py-2 text-left">Rate (NPR)</th>
                  <th className="border px-4 py-2 text-left">Amount (NPR)</th>
                  <th className="border px-4 py-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item) => (
                  <tr key={item.id}>
                    <td className="border px-4 py-2">
                      <input
                        type="text"
                        value={item.description}
                        onChange={(e) => handleItemChange(item.id, "description", e.target.value)}
                        className="w-full p-1 border rounded focus:outline-none focus:ring focus:border-blue-300"
                        placeholder="Item description"
                      />
                    </td>
                    <td className="border px-4 py-2">
                      <input
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(item.id, "quantity", Number(e.target.value))}
                        className="w-full p-1 border rounded focus:outline-none focus:ring focus:border-blue-300"
                        min="1"
                      />
                    </td>
                    <td className="border px-4 py-2">
                      <input
                        type="number"
                        value={item.rate}
                        onChange={(e) => handleItemChange(item.id, "rate", Number(e.target.value))}
                        className="w-full p-1 border rounded focus:outline-none focus:ring focus:border-blue-300"
                        min="0"
                        step="0.01"
                      />
                    </td>
                    <td className="border px-4 py-2 text-right font-medium">
                      NPR {item.amount.toLocaleString()}
                    </td>
                    <td className="border px-4 py-2">
                      <Button
                        onClick={() => removeItem(item.id)}
                        variant="outline"
                        size="sm"
                        disabled={items.length === 1}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Invoice Summary */}
        <div className="mt-6 flex justify-end">
          <div className="w-64 space-y-2">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span className="font-medium">NPR {subtotal.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span>Tax ({formData.taxRate}%):</span>
              <span className="font-medium">NPR {tax.toLocaleString()}</span>
            </div>
            <div className="border-t pt-2 flex justify-between text-lg font-bold">
              <span>Total:</span>
              <span>NPR {total.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleFormChange("notes", e.target.value)}
            className="w-full p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
            rows={3}
            placeholder="Additional notes or terms..."
          />
        </div>

        {/* Actions */}
        <div className="mt-6 flex gap-4">
          <Button
            onClick={handleSubmit}
            disabled={!isFormValid()}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Create Invoice
          </Button>
          <Button onClick={handleCancel} variant="outline">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
