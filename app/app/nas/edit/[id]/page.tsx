"use client";

import { useRouter, usePara<PERSON> } from "next/navigation";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import NASForm from "@/components/nas/nas-form";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";
import { NAS, NASFormData } from "@/types/nas";

export default function EditNASPage() {
  const router = useRouter();
  const params = useParams();
  const nasId = params.id as string;
  const [nasData, setNasData] = useState<NAS | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Try to get data from sessionStorage first (preferred method)
    const storedData = sessionStorage.getItem("editNAS");
    if (storedData) {
      try {
        const parsedData = JSON.parse(storedData);
        setNasData(parsedData);
        setLoading(false);
        return;
      } catch (error) {
        console.error("Error parsing stored NAS data:", error);
      }
    }

    // Fallback: fetch data from API if not in sessionStorage
    // const fetchNASData = async () => {
    //   try {
    //     const response = await apiClient.get(`/nas/${nasId}`);
    //     setNasData(response.data.data);
    //   } catch (error) {
    //     console.error("Failed to fetch NAS data:", error);
    //     alert("Failed to load NAS data. Redirecting to NAS list.");
    //     router.push("/app/nas");
    //   } finally {
    //     setLoading(false);
    //   }
    // };

    // fetchNASData();
  }, [nasId, router]);

  const handleSubmit = async (updatedNAS: NASFormData) => {
    try {
      const response = await apiClient.patch(`/nas/${nasId}`, updatedNAS);
      console.log("NAS updated:", response.data);
      sessionStorage.removeItem("editNAS");
      router.push("/app/nas");
    } catch (error) {
      console.error("Failed to update NAS:", error);
      alert("Failed to update NAS. Please try again.");
    }
  };

  const handleCancel = () => {
    sessionStorage.removeItem("editNAS");
    router.push("/app/nas");
  };

  if (loading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading NAS data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!nasData) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="text-center">
          <p className="text-red-600 mb-4">NAS data not found.</p>
          <Button onClick={() => router.push("/app/nas")}>
            Back to NAS List
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push("/app/nas")}
          className="mb-4 p-0 h-auto font-normal text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to NAS
        </Button>
        
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit NAS</h1>
          <p className="text-gray-600 mt-1">
            Update Network Access Server configuration for {nasData.nasname}
          </p>
        </div>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <NASForm 
          initialData={nasData}
          onSubmit={handleSubmit} 
          onCancel={handleCancel}
          isEdit={true}
        />
      </div>
    </div>
  );
}
