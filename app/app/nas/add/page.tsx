"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import NASForm from "@/components/nas/nas-form";
import { ArrowLeft } from "lucide-react";
import apiClient from "@/lib/apiClient";
import { NASFormData } from "@/types/nas";

export default function AddNASPage() {
  const router = useRouter();

  const handleSubmit = async (nasData: NASFormData) => {
    try {
      const response = await apiClient.post("/nas", nasData);
      console.log("NAS created:", response.data);
      router.push("/app/nas");
    } catch (error) {
      console.error("Failed to create NAS:", error);
      alert("Failed to create NAS. Please try again.");
    }
  };

  const handleCancel = () => {
    router.push("/app/nas");
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push("/app/nas")}
          className="mb-4 p-0 h-auto font-normal text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to NAS
        </Button>
        
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Add New NAS</h1>
          <p className="text-gray-600 mt-1">
            Create a new Network Access Server configuration
          </p>
        </div>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <NASForm 
          onSubmit={handleSubmit} 
          onCancel={handleCancel}
          isEdit={false}
        />
      </div>
    </div>
  );
}
