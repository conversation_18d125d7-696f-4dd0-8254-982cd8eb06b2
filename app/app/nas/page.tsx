"use client";

import React, { useEffect, useState } from "react";
import apiClient from "@/lib/apiClient";
import Link from "next/link";

const NasPage = () => {
  const [nasData, setNasData] = useState<any[]>([]);

  useEffect(() => {
    const fetchNas = async () => {
      try {
        const res = await apiClient.get("/nas");
        const data = res.data.data;
        setNasData(data);
      } catch (error) {
        console.error("Error fetching NAS data:", error);
      }
    };
    fetchNas();
  }, []);

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">NAS</h1>
        <Link href="/app/nas/add">
          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded shadow">
            + Add NAS
          </button>
        </Link>
      </div>
      <div className="bg-white rounded shadow p-4">
        <table className="min-w-full table-auto">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-4 py-2 text-left">SN</th>
              <th className="px-4 py-2 text-left">Vendor</th>
              <th className="px-4 py-2 text-left">NAS Name</th>
              <th className="px-4 py-2 text-left">Description</th>
              <th className="px-4 py-2 text-left">Action</th>
            </tr>
          </thead>
          <tbody>
            {nasData && nasData.length > 0 ? (
              nasData.map((item, idx) => (
                <tr key={item.id || idx} className="border-b hover:bg-gray-50">
                  <td className="px-4 py-2">{idx + 1}</td>
                  <td className="px-4 py-2">{item.vendor}</td>
                  <td className="px-4 py-2">{item.nasname}</td>
                  <td className="px-4 py-2">{item.description}</td>
                  <td className="px-4 py-2 flex gap-2">
                    <Link href={`/app/nas/edit/${item.id}`}>
                      <button className="bg-yellow-400 hover:bg-yellow-500 text-white px-3 py-1 rounded text-sm">
                        Edit
                      </button>
                    </Link>
                    <button
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default NasPage;
