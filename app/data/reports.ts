export interface RevenueData {
  month: string;
  revenue: number;
  invoices: number;
  customers: number;
}

export interface PackageUsageData {
  packageName: string;
  activeUsers: number;
  revenue: number;
  percentage: number;
}

export interface CustomerGrowthData {
  month: string;
  newCustomers: number;
  totalCustomers: number;
  churnRate: number;
}

export interface PaymentStatusData {
  status: string;
  count: number;
  amount: number;
  percentage: number;
}

// Sample revenue data for the last 12 months
export const revenueData: RevenueData[] = [
  { month: "Jan 2024", revenue: 450000, invoices: 120, customers: 1200 },
  { month: "Feb 2024", revenue: 465000, invoices: 125, customers: 1250 },
  { month: "Mar 2024", revenue: 480000, invoices: 130, customers: 1300 },
  { month: "Apr 2024", revenue: 495000, invoices: 135, customers: 1350 },
  { month: "May 2024", revenue: 510000, invoices: 140, customers: 1400 },
  { month: "Jun 2024", revenue: 525000, invoices: 145, customers: 1450 },
  { month: "Jul 2024", revenue: 540000, invoices: 150, customers: 1500 },
  { month: "Aug 2024", revenue: 555000, invoices: 155, customers: 1550 },
  { month: "Sep 2024", revenue: 570000, invoices: 160, customers: 1600 },
  { month: "Oct 2024", revenue: 585000, invoices: 165, customers: 1650 },
  { month: "Nov 2024", revenue: 600000, invoices: 170, customers: 1700 },
  { month: "Dec 2024", revenue: 615000, invoices: 175, customers: 1750 },
];

// Package usage data
export const packageUsageData: PackageUsageData[] = [
  { packageName: "100/100 Mbps", activeUsers: 800, revenue: 320000, percentage: 45.7 },
  { packageName: "200/200 Mbps", activeUsers: 600, revenue: 360000, percentage: 34.3 },
  { packageName: "300/300 Mbps", activeUsers: 300, revenue: 270000, percentage: 17.1 },
  { packageName: "50/50 Mbps", activeUsers: 50, revenue: 20000, percentage: 2.9 },
];

// Customer growth data
export const customerGrowthData: CustomerGrowthData[] = [
  { month: "Jan 2024", newCustomers: 45, totalCustomers: 1200, churnRate: 2.1 },
  { month: "Feb 2024", newCustomers: 50, totalCustomers: 1250, churnRate: 1.8 },
  { month: "Mar 2024", newCustomers: 55, totalCustomers: 1300, churnRate: 1.5 },
  { month: "Apr 2024", newCustomers: 52, totalCustomers: 1350, churnRate: 1.7 },
  { month: "May 2024", newCustomers: 48, totalCustomers: 1400, churnRate: 2.0 },
  { month: "Jun 2024", newCustomers: 53, totalCustomers: 1450, churnRate: 1.6 },
  { month: "Jul 2024", newCustomers: 58, totalCustomers: 1500, churnRate: 1.4 },
  { month: "Aug 2024", newCustomers: 55, totalCustomers: 1550, churnRate: 1.5 },
  { month: "Sep 2024", newCustomers: 60, totalCustomers: 1600, churnRate: 1.3 },
  { month: "Oct 2024", newCustomers: 52, totalCustomers: 1650, churnRate: 1.8 },
  { month: "Nov 2024", newCustomers: 55, totalCustomers: 1700, churnRate: 1.6 },
  { month: "Dec 2024", newCustomers: 58, totalCustomers: 1750, churnRate: 1.4 },
];

// Payment status data
export const paymentStatusData: PaymentStatusData[] = [
  { status: "Paid", count: 145, amount: 580000, percentage: 82.9 },
  { status: "Pending", count: 20, amount: 80000, percentage: 11.4 },
  { status: "Overdue", count: 8, amount: 32000, percentage: 4.6 },
  { status: "Cancelled", count: 2, amount: 8000, percentage: 1.1 },
];

// Report types
export const reportTypes = [
  {
    id: "revenue",
    name: "Revenue Report",
    description: "Monthly revenue trends and analysis",
    icon: "💰",
  },
  {
    id: "customers",
    name: "Customer Report",
    description: "Customer growth and retention metrics",
    icon: "👥",
  },
  {
    id: "packages",
    name: "Package Usage Report",
    description: "Package popularity and revenue breakdown",
    icon: "📦",
  },
  {
    id: "payments",
    name: "Payment Status Report",
    description: "Payment collection and outstanding amounts",
    icon: "💳",
  },
];

// Helper functions
export const formatCurrency = (amount: number) => {
  return `NPR ${amount.toLocaleString()}`;
};

export const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`;
};

export const calculateGrowthRate = (current: number, previous: number) => {
  if (previous === 0) return 0;
  return ((current - previous) / previous) * 100;
};

export const getDateRange = (period: string) => {
  const now = new Date();
  let startDate: Date;
  let endDate = new Date(now);

  switch (period) {
    case "This Month":
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case "Last Month":
      startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      endDate = new Date(now.getFullYear(), now.getMonth(), 0);
      break;
    case "Last 3 Months":
      startDate = new Date(now.getFullYear(), now.getMonth() - 3, 1);
      break;
    case "Last 6 Months":
      startDate = new Date(now.getFullYear(), now.getMonth() - 6, 1);
      break;
    case "This Year":
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    case "Last Year":
      startDate = new Date(now.getFullYear() - 1, 0, 1);
      endDate = new Date(now.getFullYear() - 1, 11, 31);
      break;
    default:
      startDate = new Date(now.getFullYear(), now.getMonth() - 12, 1);
  }

  return { startDate, endDate };
};
