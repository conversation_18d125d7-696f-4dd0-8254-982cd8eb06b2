export interface InvoiceItem {
  id: number;
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

export interface Invoice {
  id: number;
  invoiceNumber: string;
  customerId: number;
  customerName: string;
  customerEmail: string;
  issueDate: string;
  dueDate: string;
  status: "Draft" | "Sent" | "Paid" | "Overdue" | "Cancelled";
  items: InvoiceItem[];
  subtotal: number;
  tax: number;
  taxRate: number;
  total: number;
  notes?: string;
  paymentDate?: string;
}

// Sample invoice data
export const invoices: Invoice[] = [
  {
    id: 1,
    invoiceNumber: "INV-2024-001",
    customerId: 1,
    customerName: "<PERSON> <PERSON>",
    customerEmail: "<EMAIL>",
    issueDate: "2024-01-01",
    dueDate: "2024-01-31",
    status: "Paid",
    items: [
      {
        id: 1,
        description: "200Mbps Internet Package - January 2024",
        quantity: 1,
        rate: 4000,
        amount: 4000,
      },
    ],
    subtotal: 4000,
    tax: 520,
    taxRate: 13,
    total: 4520,
    notes: "Monthly internet service charge",
    paymentDate: "2024-01-15",
  },
  {
    id: 2,
    invoiceNumber: "INV-2024-002",
    customerId: 2,
    customerName: "<PERSON><PERSON>",
    customerEmail: "<EMAIL>",
    issueDate: "2024-01-01",
    dueDate: "2024-01-31",
    status: "Overdue",
    items: [
      {
        id: 1,
        description: "100Mbps Internet Package - January 2024",
        quantity: 1,
        rate: 2500,
        amount: 2500,
      },
      {
        id: 2,
        description: "Router Rental",
        quantity: 1,
        rate: 500,
        amount: 500,
      },
    ],
    subtotal: 3000,
    tax: 390,
    taxRate: 13,
    total: 3390,
    notes: "Monthly charges including router rental",
  },
  {
    id: 3,
    invoiceNumber: "INV-2024-003",
    customerId: 3,
    customerName: "Ram Prasad Yadav",
    customerEmail: "<EMAIL>",
    issueDate: "2024-01-15",
    dueDate: "2024-02-14",
    status: "Sent",
    items: [
      {
        id: 1,
        description: "300Mbps Internet Package - January 2024",
        quantity: 1,
        rate: 6000,
        amount: 6000,
      },
    ],
    subtotal: 6000,
    tax: 780,
    taxRate: 13,
    total: 6780,
    notes: "Premium package subscription",
  },
  {
    id: 4,
    invoiceNumber: "INV-2024-004",
    customerId: 4,
    customerName: "Gita Poudel",
    customerEmail: "<EMAIL>",
    issueDate: "2024-01-20",
    dueDate: "2024-02-19",
    status: "Draft",
    items: [
      {
        id: 1,
        description: "100Mbps Internet Package - February 2024",
        quantity: 1,
        rate: 2500,
        amount: 2500,
      },
    ],
    subtotal: 2500,
    tax: 325,
    taxRate: 13,
    total: 2825,
    notes: "Draft invoice for February billing",
  },
  {
    id: 5,
    invoiceNumber: "INV-2024-005",
    customerId: 5,
    customerName: "Krishna Bahadur Thapa",
    customerEmail: "<EMAIL>",
    issueDate: "2024-01-25",
    dueDate: "2024-02-24",
    status: "Sent",
    items: [
      {
        id: 1,
        description: "200Mbps Internet Package - February 2024",
        quantity: 1,
        rate: 4000,
        amount: 4000,
      },
      {
        id: 2,
        description: "Installation Fee",
        quantity: 1,
        rate: 1500,
        amount: 1500,
      },
    ],
    subtotal: 5500,
    tax: 715,
    taxRate: 13,
    total: 6215,
    notes: "New connection with installation",
  },
];

// Helper functions
export const getInvoiceStatusColor = (status: Invoice["status"]) => {
  switch (status) {
    case "Draft":
      return "bg-gray-100 text-gray-800";
    case "Sent":
      return "bg-blue-100 text-blue-800";
    case "Paid":
      return "bg-green-100 text-green-800";
    case "Overdue":
      return "bg-red-100 text-red-800";
    case "Cancelled":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const calculateInvoiceTotals = (items: InvoiceItem[], taxRate: number = 13) => {
  const subtotal = items.reduce((sum, item) => sum + item.amount, 0);
  const tax = (subtotal * taxRate) / 100;
  const total = subtotal + tax;
  
  return { subtotal, tax, total };
};

export const generateInvoiceNumber = () => {
  const year = new Date().getFullYear();
  const timestamp = Date.now().toString().slice(-6);
  return `INV-${year}-${timestamp}`;
};
