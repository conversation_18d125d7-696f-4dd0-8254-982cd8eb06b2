{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "types": ["node", "react", "react-dom"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "typeRoots": ["node_modules/@types", "@types"], "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "noImplicitAny": false, "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}